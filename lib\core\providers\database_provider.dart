import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../datasources/database.dart';
import '../services/database_service.dart';

part 'database_provider.g.dart';

/// Global database provider
///
/// This provider creates and manages the application database instance.
/// It's a singleton that will be created once and reused throughout the app.
@Riverpod(keepAlive: true)
AppDatabase database(DatabaseRef ref) {
  final database = AppDatabase();

  // Ensure the database is properly closed when the provider is disposed
  ref.onDispose(() {
    database.close();
  });

  return database;
}

/// Database service provider
///
/// This provider creates and manages the database service instance.
@Riverpod(keepAlive: true)
DatabaseService databaseService(Ref ref) {
  return DatabaseService.instance;
}

/// Database initialization provider
///
/// This provider handles database initialization and returns the initialized state.
@riverpod
Future<bool> databaseInitialization(Ref ref) async {
  final service = ref.watch(databaseServiceProvider);

  try {
    await service.initialize();
    return true;
  } on Exception {
    return false;
  }
}

/// Database connection state provider
///
/// This provider tracks the database connection state for UI feedback
@riverpod
Future<bool> databaseConnectionState(Ref ref) async {
  final database = ref.watch(databaseProvider);

  try {
    // Test the database connection by running a simple query
    await database.customSelect('SELECT 1').getSingle();
    return true;
  } on Exception {
    return false;
  }
}

/// Database statistics provider
///
/// This provider provides database statistics for monitoring
@riverpod
Future<DatabaseStats> databaseStats(Ref ref) async {
  final service = ref.watch(databaseServiceProvider);
  return await service.getStats();
}
