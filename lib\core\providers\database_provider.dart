import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../datasources/database.dart';

part 'database_provider.g.dart';

/// Global database provider
/// 
/// This provider creates and manages the application database instance.
/// It's a singleton that will be created once and reused throughout the app.
@Riverpod(keepAlive: true)
AppDatabase database(DatabaseRef ref) {
  final database = AppDatabase();
  
  // Ensure the database is properly closed when the provider is disposed
  ref.onDispose(() {
    database.close();
  });
  
  return database;
}

/// Database connection state provider
/// 
/// This provider tracks the database connection state for UI feedback
@riverpod
Future<bool> databaseConnectionState(DatabaseConnectionStateRef ref) async {
  final database = ref.watch(databaseProvider);
  
  try {
    // Test the database connection by running a simple query
    await database.customSelect('SELECT 1').getSingle();
    return true;
  } catch (e) {
    return false;
  }
}
