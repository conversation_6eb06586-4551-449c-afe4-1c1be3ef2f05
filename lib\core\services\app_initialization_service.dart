import 'package:flutter/foundation.dart';
import 'database_service.dart';

/// App initialization service
/// 
/// This service handles the complete application initialization process,
/// including database setup, migrations, and other startup tasks.
class AppInitializationService {
  static AppInitializationService? _instance;
  
  AppInitializationService._();
  
  /// Get singleton instance
  static AppInitializationService get instance {
    _instance ??= AppInitializationService._();
    return _instance!;
  }
  
  bool _isInitialized = false;
  
  /// Check if app is initialized
  bool get isInitialized => _isInitialized;
  
  /// Initialize the application
  Future<void> initialize() async {
    if (_isInitialized) {
      return; // Already initialized
    }
    
    try {
      debugPrint('Starting app initialization...');
      
      // Step 1: Initialize database
      await _initializeDatabase();
      
      // Step 2: Run any necessary migrations
      await _runMigrations();
      
      // Step 3: Perform initial data setup
      await _setupInitialData();
      
      // Step 4: Validate initialization
      await _validateInitialization();
      
      _isInitialized = true;
      debugPrint('App initialization completed successfully');
      
    } catch (e) {
      debugPrint('App initialization failed: $e');
      rethrow;
    }
  }
  
  /// Initialize database
  Future<void> _initializeDatabase() async {
    debugPrint('Initializing database...');
    
    final databaseService = DatabaseService.instance;
    await databaseService.initialize();
    
    // Check database integrity
    final isIntegrityOk = await databaseService.checkIntegrity();
    if (!isIntegrityOk) {
      throw Exception('Database integrity check failed during initialization');
    }
    
    debugPrint('Database initialized successfully');
  }
  
  /// Run database migrations
  Future<void> _runMigrations() async {
    debugPrint('Checking for database migrations...');
    
    final databaseService = DatabaseService.instance;
    final currentVersion = await databaseService.getSchemaVersion();
    const targetVersion = 1; // Current schema version
    
    if (currentVersion < targetVersion) {
      debugPrint('Running migrations from version $currentVersion to $targetVersion');
      
      // Run migration logic here
      await _runMigrationToVersion1();
      
      // Update schema version
      await databaseService.setSchemaVersion(targetVersion);
      
      debugPrint('Migrations completed successfully');
    } else {
      debugPrint('Database is up to date (version $currentVersion)');
    }
  }
  
  /// Run migration to version 1
  Future<void> _runMigrationToVersion1() async {
    // This is where you would add migration logic
    // For now, we just ensure the tables exist (handled by Drift automatically)
    debugPrint('Migration to version 1: Tables created automatically by Drift');
  }
  
  /// Setup initial data
  Future<void> _setupInitialData() async {
    debugPrint('Setting up initial data...');
    
    // Add any initial data setup here
    // For example, default settings, sample data, etc.
    
    debugPrint('Initial data setup completed');
  }
  
  /// Validate initialization
  Future<void> _validateInitialization() async {
    debugPrint('Validating initialization...');
    
    final databaseService = DatabaseService.instance;
    
    // Test database connection
    final database = databaseService.database;
    await database.customSelect('SELECT 1').getSingle();
    
    // Get database stats
    final stats = await databaseService.getStats();
    debugPrint('Database validation: $stats');
    
    debugPrint('Initialization validation completed');
  }
  
  /// Reset initialization state (for testing)
  void reset() {
    _isInitialized = false;
  }
  
  /// Get initialization status
  InitializationStatus getStatus() {
    return InitializationStatus(
      isInitialized: _isInitialized,
      timestamp: DateTime.now(),
    );
  }
}

/// Initialization status
class InitializationStatus {
  final bool isInitialized;
  final DateTime timestamp;
  
  const InitializationStatus({
    required this.isInitialized,
    required this.timestamp,
  });
  
  @override
  String toString() {
    return 'InitializationStatus('
        'isInitialized: $isInitialized, '
        'timestamp: $timestamp'
        ')';
  }
}
