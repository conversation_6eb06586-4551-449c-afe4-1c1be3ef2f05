import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as p;
import '../datasources/database.dart';

/// Database service for initialization and management
class DatabaseService {
  static DatabaseService? _instance;
  static AppDatabase? _database;

  DatabaseService._();

  /// Get singleton instance
  static DatabaseService get instance {
    _instance ??= DatabaseService._();
    return _instance!;
  }

  /// Get database instance
  AppDatabase get database {
    if (_database == null) {
      throw Exception('Database not initialized. Call initialize() first.');
    }
    return _database!;
  }

  /// Initialize the database
  Future<void> initialize() async {
    if (_database != null) {
      return; // Already initialized
    }

    try {
      _database = AppDatabase();
      
      // Test the database connection
      await _testConnection();
      
      print('Database initialized successfully');
    } catch (e) {
      print('Failed to initialize database: $e');
      rethrow;
    }
  }

  /// Test database connection
  Future<void> _testConnection() async {
    try {
      await _database!.customSelect('SELECT 1').getSingle();
    } catch (e) {
      throw Exception('Database connection test failed: $e');
    }
  }

  /// Close the database connection
  Future<void> close() async {
    if (_database != null) {
      await _database!.close();
      _database = null;
    }
  }

  /// Get database file path
  Future<String> getDatabasePath() async {
    final dbFolder = await getApplicationDocumentsDirectory();
    return p.join(dbFolder.path, 'drivly_database.db');
  }

  /// Check if database file exists
  Future<bool> databaseExists() async {
    final path = await getDatabasePath();
    return File(path).exists();
  }

  /// Get database file size in bytes
  Future<int> getDatabaseSize() async {
    final path = await getDatabasePath();
    final file = File(path);
    if (await file.exists()) {
      return await file.length();
    }
    return 0;
  }

  /// Delete database file (for testing or reset purposes)
  Future<void> deleteDatabase() async {
    await close();
    final path = await getDatabasePath();
    final file = File(path);
    if (await file.exists()) {
      await file.delete();
      print('Database file deleted: $path');
    }
  }

  /// Backup database to a specific path
  Future<void> backupDatabase(String backupPath) async {
    final sourcePath = await getDatabasePath();
    final sourceFile = File(sourcePath);
    
    if (!await sourceFile.exists()) {
      throw Exception('Database file does not exist');
    }
    
    await sourceFile.copy(backupPath);
    print('Database backed up to: $backupPath');
  }

  /// Restore database from backup
  Future<void> restoreDatabase(String backupPath) async {
    final backupFile = File(backupPath);
    
    if (!await backupFile.exists()) {
      throw Exception('Backup file does not exist: $backupPath');
    }
    
    // Close current database
    await close();
    
    // Copy backup to database location
    final targetPath = await getDatabasePath();
    await backupFile.copy(targetPath);
    
    // Reinitialize database
    await initialize();
    
    print('Database restored from: $backupPath');
  }

  /// Get database statistics
  Future<DatabaseStats> getStats() async {
    final db = database;
    
    // Get table counts
    final incomeCount = await db.customSelect(
      'SELECT COUNT(*) as count FROM income WHERE deleted_at IS NULL'
    ).getSingle();
    
    final deletedIncomeCount = await db.customSelect(
      'SELECT COUNT(*) as count FROM income WHERE deleted_at IS NOT NULL'
    ).getSingle();
    
    final pendingSyncCount = await db.customSelect(
      'SELECT COUNT(*) as count FROM income WHERE sync_status = ?',
      variables: [Variable.withString('pendingUpload')]
    ).getSingle();
    
    return DatabaseStats(
      incomeRecords: incomeCount.read<int>('count') ?? 0,
      deletedRecords: deletedIncomeCount.read<int>('count') ?? 0,
      pendingSyncRecords: pendingSyncCount.read<int>('count') ?? 0,
      databaseSize: await getDatabaseSize(),
    );
  }

  /// Vacuum database to optimize storage
  Future<void> vacuum() async {
    await database.customStatement('VACUUM');
    print('Database vacuumed successfully');
  }

  /// Analyze database for query optimization
  Future<void> analyze() async {
    await database.customStatement('ANALYZE');
    print('Database analyzed successfully');
  }

  /// Check database integrity
  Future<bool> checkIntegrity() async {
    try {
      final result = await database.customSelect('PRAGMA integrity_check').getSingle();
      final status = result.read<String>('integrity_check');
      return status == 'ok';
    } catch (e) {
      print('Database integrity check failed: $e');
      return false;
    }
  }

  /// Get database schema version
  Future<int> getSchemaVersion() async {
    try {
      final result = await database.customSelect('PRAGMA user_version').getSingle();
      return result.read<int>('user_version') ?? 0;
    } catch (e) {
      print('Failed to get schema version: $e');
      return 0;
    }
  }

  /// Set database schema version
  Future<void> setSchemaVersion(int version) async {
    await database.customStatement('PRAGMA user_version = $version');
  }

  /// Perform database maintenance
  Future<void> performMaintenance() async {
    print('Starting database maintenance...');
    
    // Check integrity
    final isIntegrityOk = await checkIntegrity();
    if (!isIntegrityOk) {
      throw Exception('Database integrity check failed');
    }
    
    // Analyze for optimization
    await analyze();
    
    // Vacuum to reclaim space
    await vacuum();
    
    print('Database maintenance completed successfully');
  }
}

/// Database statistics
class DatabaseStats {
  final int incomeRecords;
  final int deletedRecords;
  final int pendingSyncRecords;
  final int databaseSize;

  const DatabaseStats({
    required this.incomeRecords,
    required this.deletedRecords,
    required this.pendingSyncRecords,
    required this.databaseSize,
  });

  /// Get formatted database size
  String get formattedSize {
    if (databaseSize < 1024) {
      return '${databaseSize}B';
    } else if (databaseSize < 1024 * 1024) {
      return '${(databaseSize / 1024).toStringAsFixed(1)}KB';
    } else {
      return '${(databaseSize / (1024 * 1024)).toStringAsFixed(1)}MB';
    }
  }

  /// Get total records count
  int get totalRecords => incomeRecords + deletedRecords;

  @override
  String toString() {
    return 'DatabaseStats('
        'incomeRecords: $incomeRecords, '
        'deletedRecords: $deletedRecords, '
        'pendingSyncRecords: $pendingSyncRecords, '
        'databaseSize: $formattedSize'
        ')';
  }
}
