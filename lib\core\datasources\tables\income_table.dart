import 'package:drift/drift.dart';

/// Income table definition following the PRD schema
@DataClassName('IncomeTableData')
class IncomeTable extends Table {
  /// Primary key - UUID
  TextColumn get uuid => text()();
  
  /// Auto-increment ID for internal use
  IntColumn get id => integer().autoIncrement()();
  
  /// Date of the income record
  DateTimeColumn get date => dateTime()();
  
  /// Initial mileage reading
  IntColumn get initialMileage => integer()();
  
  /// Final mileage reading
  IntColumn get finalMileage => integer()();
  
  /// Initial GoPay balance
  RealColumn get initialGopay => real()();
  
  /// Initial BCA balance
  RealColumn get initialBca => real()();
  
  /// Initial Cash amount
  RealColumn get initialCash => real()();
  
  /// Initial OVO balance
  RealColumn get initialOvo => real()();
  
  /// Initial BRI balance
  RealColumn get initialBri => real()();
  
  /// Initial Rekpon balance
  RealColumn get initialRekpon => real()();
  
  /// Final GoPay balance
  RealColumn get finalGopay => real()();
  
  /// Final BCA balance
  RealColumn get finalBca => real()();
  
  /// Final Cash amount
  RealColumn get finalCash => real()();
  
  /// Final OVO balance
  RealColumn get finalOvo => real()();
  
  /// Final BRI balance
  RealColumn get finalBri => real()();
  
  /// Final Rekpon balance
  RealColumn get finalRekpon => real()();
  
  /// Initial capital amount
  RealColumn get initialCapital => real()();
  
  /// Final result amount
  RealColumn get finalResult => real()();
  
  /// Computed mileage (final - initial)
  IntColumn get mileage => integer().generatedAs(
    finalMileage - initialMileage,
    stored: true,
  )();
  
  /// Computed net income (total final - total initial)
  RealColumn get netIncome => real().generatedAs(
    (finalGopay + finalBca + finalCash + finalOvo + finalBri + finalRekpon) -
    (initialGopay + initialBca + initialCash + initialOvo + initialBri + initialRekpon),
    stored: true,
  )();
  
  /// Record creation timestamp
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
  
  /// Record update timestamp
  DateTimeColumn get updatedAt => dateTime().withDefault(currentDateAndTime)();
  
  /// Soft delete timestamp (null if not deleted)
  DateTimeColumn get deletedAt => dateTime().nullable()();
  
  /// Sync status for cloud synchronization
  TextColumn get syncStatus => text().withDefault(const Constant('pendingUpload'))();
  
  @override
  Set<Column> get primaryKey => {uuid};
  
  @override
  List<String> get customConstraints => [
    // Ensure mileage values are non-negative
    'CHECK (initial_mileage >= 0)',
    'CHECK (final_mileage >= initial_mileage)',
    
    // Ensure monetary values are non-negative
    'CHECK (initial_gopay >= 0)',
    'CHECK (initial_bca >= 0)',
    'CHECK (initial_cash >= 0)',
    'CHECK (initial_ovo >= 0)',
    'CHECK (initial_bri >= 0)',
    'CHECK (initial_rekpon >= 0)',
    'CHECK (final_gopay >= 0)',
    'CHECK (final_bca >= 0)',
    'CHECK (final_cash >= 0)',
    'CHECK (final_ovo >= 0)',
    'CHECK (final_bri >= 0)',
    'CHECK (final_rekpon >= 0)',
    'CHECK (initial_capital >= 0)',
    'CHECK (final_result >= 0)',
    
    // Ensure sync status has valid values
    'CHECK (sync_status IN ("pendingUpload", "synced", "failed", "conflict"))',
  ];
}

/// Extension methods for IncomeTableData
extension IncomeTableDataExtension on IncomeTableData {
  /// Calculate total initial amount
  double get totalInitialAmount =>
      initialGopay + initialBca + initialCash + initialOvo + initialBri + initialRekpon;
  
  /// Calculate total final amount
  double get totalFinalAmount =>
      finalGopay + finalBca + finalCash + finalOvo + finalBri + finalRekpon;
  
  /// Calculate profit/loss
  double get profitLoss => totalFinalAmount - totalInitialAmount;
  
  /// Check if record is deleted
  bool get isDeleted => deletedAt != null;
  
  /// Check if record needs sync
  bool get needsSync => syncStatus == 'pendingUpload' || syncStatus == 'failed';
  
  /// Get formatted date string
  String get formattedDate => '${date.day}/${date.month}/${date.year}';
  
  /// Get payment method breakdown
  Map<String, Map<String, double>> get paymentBreakdown => {
    'GoPay': {'initial': initialGopay, 'final': finalGopay, 'difference': finalGopay - initialGopay},
    'BCA': {'initial': initialBca, 'final': finalBca, 'difference': finalBca - initialBca},
    'Cash': {'initial': initialCash, 'final': finalCash, 'difference': finalCash - initialCash},
    'OVO': {'initial': initialOvo, 'final': finalOvo, 'difference': finalOvo - initialOvo},
    'BRI': {'initial': initialBri, 'final': finalBri, 'difference': finalBri - initialBri},
    'Rekpon': {'initial': initialRekpon, 'final': finalRekpon, 'difference': finalRekpon - initialRekpon},
  };
}
