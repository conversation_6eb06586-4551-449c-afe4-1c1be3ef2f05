// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'income_usecases_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$createIncomeUseCaseHash() =>
    r'5f172c21b032878ad185cc4863d8e3062042ab31';

/// Create income use case provider
///
/// Copied from [createIncomeUseCase].
@ProviderFor(createIncomeUseCase)
final createIncomeUseCaseProvider =
    AutoDisposeProvider<CreateIncomeUseCase>.internal(
      createIncomeUseCase,
      name: r'createIncomeUseCaseProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$createIncomeUseCaseHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CreateIncomeUseCaseRef = AutoDisposeProviderRef<CreateIncomeUseCase>;
String _$updateIncomeUseCaseHash() =>
    r'4ab51c2124cdbefbccf3b8766e46f95299129350';

/// Update income use case provider
///
/// Copied from [updateIncomeUseCase].
@ProviderFor(updateIncomeUseCase)
final updateIncomeUseCaseProvider =
    AutoDisposeProvider<UpdateIncomeUseCase>.internal(
      updateIncomeUseCase,
      name: r'updateIncomeUseCaseProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$updateIncomeUseCaseHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef UpdateIncomeUseCaseRef = AutoDisposeProviderRef<UpdateIncomeUseCase>;
String _$getIncomesUseCaseHash() => r'aea58a0cffe0acecf7272a6fdcf1b2caa43bcbbf';

/// Get incomes use case provider
///
/// Copied from [getIncomesUseCase].
@ProviderFor(getIncomesUseCase)
final getIncomesUseCaseProvider =
    AutoDisposeProvider<GetIncomesUseCase>.internal(
      getIncomesUseCase,
      name: r'getIncomesUseCaseProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$getIncomesUseCaseHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef GetIncomesUseCaseRef = AutoDisposeProviderRef<GetIncomesUseCase>;
String _$deleteIncomeUseCaseHash() =>
    r'215fd4bbd4ba8822152a198d478573b1dde74052';

/// Delete income use case provider
///
/// Copied from [deleteIncomeUseCase].
@ProviderFor(deleteIncomeUseCase)
final deleteIncomeUseCaseProvider =
    AutoDisposeProvider<DeleteIncomeUseCase>.internal(
      deleteIncomeUseCase,
      name: r'deleteIncomeUseCaseProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$deleteIncomeUseCaseHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef DeleteIncomeUseCaseRef = AutoDisposeProviderRef<DeleteIncomeUseCase>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
