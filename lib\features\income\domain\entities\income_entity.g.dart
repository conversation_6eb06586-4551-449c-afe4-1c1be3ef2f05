// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'income_entity.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$IncomeEntityImpl _$$IncomeEntityImplFromJson(
  Map<String, dynamic> json,
) => $checkedCreate(r'_$IncomeEntityImpl', json, ($checkedConvert) {
  final val = _$IncomeEntityImpl(
    uuid: $checkedConvert('uuid', (v) => v as String),
    id: $checkedConvert('id', (v) => (v as num).toInt()),
    date: $checkedConvert('date', (v) => DateTime.parse(v as String)),
    initialMileage: $checkedConvert(
      'initialMileage',
      (v) => (v as num).toInt(),
    ),
    finalMileage: $checkedConvert('finalMileage', (v) => (v as num).toInt()),
    initialGopay: $checkedConvert('initialGopay', (v) => (v as num).toDouble()),
    initialBca: $checkedConvert('initialBca', (v) => (v as num).toDouble()),
    initialCash: $checkedConvert('initialCash', (v) => (v as num).toDouble()),
    initialOvo: $checkedConvert('initialOvo', (v) => (v as num).toDouble()),
    initialBri: $checkedConvert('initialBri', (v) => (v as num).toDouble()),
    initialRekpon: $checkedConvert(
      'initialRekpon',
      (v) => (v as num).toDouble(),
    ),
    finalGopay: $checkedConvert('finalGopay', (v) => (v as num).toDouble()),
    finalBca: $checkedConvert('finalBca', (v) => (v as num).toDouble()),
    finalCash: $checkedConvert('finalCash', (v) => (v as num).toDouble()),
    finalOvo: $checkedConvert('finalOvo', (v) => (v as num).toDouble()),
    finalBri: $checkedConvert('finalBri', (v) => (v as num).toDouble()),
    finalRekpon: $checkedConvert('finalRekpon', (v) => (v as num).toDouble()),
    initialCapital: $checkedConvert(
      'initialCapital',
      (v) => (v as num).toDouble(),
    ),
    finalResult: $checkedConvert('finalResult', (v) => (v as num).toDouble()),
    createdAt: $checkedConvert('createdAt', (v) => DateTime.parse(v as String)),
    updatedAt: $checkedConvert('updatedAt', (v) => DateTime.parse(v as String)),
    deletedAt: $checkedConvert(
      'deletedAt',
      (v) => v == null ? null : DateTime.parse(v as String),
    ),
    syncStatus: $checkedConvert(
      'syncStatus',
      (v) => v as String? ?? 'pendingUpload',
    ),
  );
  return val;
});

Map<String, dynamic> _$$IncomeEntityImplToJson(_$IncomeEntityImpl instance) =>
    <String, dynamic>{
      'uuid': instance.uuid,
      'id': instance.id,
      'date': instance.date.toIso8601String(),
      'initialMileage': instance.initialMileage,
      'finalMileage': instance.finalMileage,
      'initialGopay': instance.initialGopay,
      'initialBca': instance.initialBca,
      'initialCash': instance.initialCash,
      'initialOvo': instance.initialOvo,
      'initialBri': instance.initialBri,
      'initialRekpon': instance.initialRekpon,
      'finalGopay': instance.finalGopay,
      'finalBca': instance.finalBca,
      'finalCash': instance.finalCash,
      'finalOvo': instance.finalOvo,
      'finalBri': instance.finalBri,
      'finalRekpon': instance.finalRekpon,
      'initialCapital': instance.initialCapital,
      'finalResult': instance.finalResult,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      if (instance.deletedAt?.toIso8601String() case final value?)
        'deletedAt': value,
      'syncStatus': instance.syncStatus,
    };

_$PaymentMethodDataImpl _$$PaymentMethodDataImplFromJson(
  Map<String, dynamic> json,
) => $checkedCreate(r'_$PaymentMethodDataImpl', json, ($checkedConvert) {
  final val = _$PaymentMethodDataImpl(
    name: $checkedConvert('name', (v) => v as String),
    initial: $checkedConvert('initial', (v) => (v as num).toDouble()),
    finalAmount: $checkedConvert('finalAmount', (v) => (v as num).toDouble()),
    difference: $checkedConvert('difference', (v) => (v as num).toDouble()),
  );
  return val;
});

Map<String, dynamic> _$$PaymentMethodDataImplToJson(
  _$PaymentMethodDataImpl instance,
) => <String, dynamic>{
  'name': instance.name,
  'initial': instance.initial,
  'finalAmount': instance.finalAmount,
  'difference': instance.difference,
};
