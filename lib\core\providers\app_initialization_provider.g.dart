// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_initialization_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$appInitializationServiceHash() =>
    r'48035b8131df724a4ae6d12280731b702c4646b5';

/// App initialization service provider
///
/// This provider creates and manages the app initialization service instance.
///
/// Copied from [appInitializationService].
@ProviderFor(appInitializationService)
final appInitializationServiceProvider =
    Provider<AppInitializationService>.internal(
      appInitializationService,
      name: r'appInitializationServiceProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$appInitializationServiceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AppInitializationServiceRef = ProviderRef<AppInitializationService>;
String _$appInitializationHash() => r'e89a35ef7b7df96b4ec651079d43d43ddf8bdf90';

/// App initialization provider
///
/// This provider handles the app initialization process and returns the status.
///
/// Copied from [appInitialization].
@ProviderFor(appInitialization)
final appInitializationProvider = AutoDisposeFutureProvider<bool>.internal(
  appInitialization,
  name: r'appInitializationProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$appInitializationHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AppInitializationRef = AutoDisposeFutureProviderRef<bool>;
String _$appInitializationStatusHash() =>
    r'b4aebecfa3b55252e0fa939bf89b0d2857513d2c';

/// App initialization status provider
///
/// This provider provides the current initialization status.
///
/// Copied from [appInitializationStatus].
@ProviderFor(appInitializationStatus)
final appInitializationStatusProvider =
    AutoDisposeProvider<InitializationStatus>.internal(
      appInitializationStatus,
      name: r'appInitializationStatusProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$appInitializationStatusHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AppInitializationStatusRef =
    AutoDisposeProviderRef<InitializationStatus>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
