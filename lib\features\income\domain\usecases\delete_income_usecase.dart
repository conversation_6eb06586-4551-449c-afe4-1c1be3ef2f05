import '../entities/income_entity.dart';
import '../repositories/income_repository.dart';

/// Use case for deleting income records
/// 
/// This use case handles the business logic for deleting income records,
/// supporting both soft delete and hard delete operations.
class DeleteIncomeUseCase {
  final IncomeRepository _repository;

  DeleteIncomeUseCase(this._repository);

  /// Soft delete an income record
  /// 
  /// Marks the record as deleted without actually removing it from the database.
  /// This allows for recovery and maintains data integrity.
  Future<IncomeEntity> executeSoftDelete(String uuid) async {
    // Validate input
    if (uuid.isEmpty) {
      throw ArgumentError('Income UUID cannot be empty');
    }

    // Check if record exists
    final existingIncome = await _repository.getIncomeByUuid(uuid);
    if (existingIncome == null) {
      throw ArgumentError('Income record not found: $uuid');
    }

    // Check if already deleted
    if (existingIncome.isDeleted) {
      throw ArgumentError('Income record is already deleted: $uuid');
    }

    // Perform soft delete
    return await _repository.softDeleteIncome(uuid);
  }

  /// Hard delete an income record
  /// 
  /// Permanently removes the record from the database.
  /// This operation cannot be undone.
  Future<void> executeHardDelete(String uuid) async {
    // Validate input
    if (uuid.isEmpty) {
      throw ArgumentError('Income UUID cannot be empty');
    }

    // Check if record exists
    final existingIncome = await _repository.getIncomeByUuid(uuid);
    if (existingIncome == null) {
      throw ArgumentError('Income record not found: $uuid');
    }

    // Perform hard delete
    await _repository.deleteIncome(uuid);
  }

  /// Restore a soft-deleted income record
  /// 
  /// Restores a previously soft-deleted record by clearing the deleted_at field.
  Future<IncomeEntity> executeRestore(String uuid) async {
    // Validate input
    if (uuid.isEmpty) {
      throw ArgumentError('Income UUID cannot be empty');
    }

    // Get the record (including soft-deleted ones)
    final existingIncome = await _repository.getIncomeByUuid(uuid);
    if (existingIncome == null) {
      throw ArgumentError('Income record not found: $uuid');
    }

    // Check if it's actually deleted
    if (!existingIncome.isDeleted) {
      throw ArgumentError('Income record is not deleted: $uuid');
    }

    // Restore the record by updating it with null deleted_at
    final restoredIncome = existingIncome.copyWith(
      deletedAt: null,
      updatedAt: DateTime.now(),
      syncStatus: 'pendingUpload',
    );

    return await _repository.updateIncome(restoredIncome);
  }

  /// Bulk soft delete multiple income records
  /// 
  /// Soft deletes multiple records in a batch operation.
  Future<List<IncomeEntity>> executeBulkSoftDelete(List<String> uuids) async {
    // Validate input
    if (uuids.isEmpty) {
      throw ArgumentError('UUID list cannot be empty');
    }

    if (uuids.any((uuid) => uuid.isEmpty)) {
      throw ArgumentError('UUID list contains empty values');
    }

    final results = <IncomeEntity>[];
    final errors = <String>[];

    // Process each UUID
    for (final uuid in uuids) {
      try {
        final result = await executeSoftDelete(uuid);
        results.add(result);
      } catch (e) {
        errors.add('Failed to delete $uuid: $e');
      }
    }

    // If there were any errors, throw an exception with details
    if (errors.isNotEmpty) {
      throw Exception('Bulk delete completed with errors:\n${errors.join('\n')}');
    }

    return results;
  }

  /// Bulk hard delete multiple income records
  /// 
  /// Permanently deletes multiple records in a batch operation.
  Future<void> executeBulkHardDelete(List<String> uuids) async {
    // Validate input
    if (uuids.isEmpty) {
      throw ArgumentError('UUID list cannot be empty');
    }

    if (uuids.any((uuid) => uuid.isEmpty)) {
      throw ArgumentError('UUID list contains empty values');
    }

    final errors = <String>[];

    // Process each UUID
    for (final uuid in uuids) {
      try {
        await executeHardDelete(uuid);
      } catch (e) {
        errors.add('Failed to delete $uuid: $e');
      }
    }

    // If there were any errors, throw an exception with details
    if (errors.isNotEmpty) {
      throw Exception('Bulk hard delete completed with errors:\n${errors.join('\n')}');
    }
  }

  /// Get all soft-deleted income records
  /// 
  /// Returns a list of all records that have been soft-deleted.
  Future<List<IncomeEntity>> getDeletedIncomes() async {
    final allIncomes = await _repository.getAllIncomes();
    return allIncomes.where((income) => income.isDeleted).toList();
  }

  /// Permanently delete all soft-deleted records
  /// 
  /// This is a cleanup operation that permanently removes all soft-deleted records.
  /// Use with caution as this operation cannot be undone.
  Future<int> cleanupDeletedRecords() async {
    final deletedIncomes = await getDeletedIncomes();
    
    if (deletedIncomes.isEmpty) {
      return 0;
    }

    final uuids = deletedIncomes.map((income) => income.uuid).toList();
    
    try {
      await executeBulkHardDelete(uuids);
      return uuids.length;
    } catch (e) {
      // If bulk delete fails, try individual deletes and count successes
      int successCount = 0;
      for (final uuid in uuids) {
        try {
          await executeHardDelete(uuid);
          successCount++;
        } catch (e) {
          // Log individual failures but continue
          print('Failed to cleanup deleted record $uuid: $e');
        }
      }
      return successCount;
    }
  }
}

/// Parameters for delete operations
class DeleteIncomeParams {
  final String uuid;
  final bool softDelete;

  const DeleteIncomeParams({
    required this.uuid,
    this.softDelete = true,
  });

  /// Create for soft delete
  factory DeleteIncomeParams.softDelete(String uuid) {
    return DeleteIncomeParams(uuid: uuid, softDelete: true);
  }

  /// Create for hard delete
  factory DeleteIncomeParams.hardDelete(String uuid) {
    return DeleteIncomeParams(uuid: uuid, softDelete: false);
  }

  @override
  String toString() {
    return 'DeleteIncomeParams(uuid: $uuid, softDelete: $softDelete)';
  }
}

/// Parameters for bulk delete operations
class BulkDeleteIncomeParams {
  final List<String> uuids;
  final bool softDelete;

  const BulkDeleteIncomeParams({
    required this.uuids,
    this.softDelete = true,
  });

  /// Create for bulk soft delete
  factory BulkDeleteIncomeParams.softDelete(List<String> uuids) {
    return BulkDeleteIncomeParams(uuids: uuids, softDelete: true);
  }

  /// Create for bulk hard delete
  factory BulkDeleteIncomeParams.hardDelete(List<String> uuids) {
    return BulkDeleteIncomeParams(uuids: uuids, softDelete: false);
  }

  @override
  String toString() {
    return 'BulkDeleteIncomeParams(uuids: ${uuids.length} items, softDelete: $softDelete)';
  }
}
