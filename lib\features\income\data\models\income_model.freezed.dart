// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'income_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

IncomeModel _$IncomeModelFromJson(Map<String, dynamic> json) {
  return _IncomeModel.fromJson(json);
}

/// @nodoc
mixin _$IncomeModel {
  String get uuid => throw _privateConstructorUsedError;
  int get id => throw _privateConstructorUsedError;
  DateTime get date => throw _privateConstructorUsedError;
  int get initialMileage => throw _privateConstructorUsedError;
  int get finalMileage => throw _privateConstructorUsedError;
  double get initialGopay => throw _privateConstructorUsedError;
  double get initialBca => throw _privateConstructorUsedError;
  double get initialCash => throw _privateConstructorUsedError;
  double get initialOvo => throw _privateConstructorUsedError;
  double get initialBri => throw _privateConstructorUsedError;
  double get initialRekpon => throw _privateConstructorUsedError;
  double get finalGopay => throw _privateConstructorUsedError;
  double get finalBca => throw _privateConstructorUsedError;
  double get finalCash => throw _privateConstructorUsedError;
  double get finalOvo => throw _privateConstructorUsedError;
  double get finalBri => throw _privateConstructorUsedError;
  double get finalRekpon => throw _privateConstructorUsedError;
  double get initialCapital => throw _privateConstructorUsedError;
  double get finalResult => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime get updatedAt => throw _privateConstructorUsedError;
  DateTime? get deletedAt => throw _privateConstructorUsedError;
  String get syncStatus => throw _privateConstructorUsedError;

  /// Serializes this IncomeModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of IncomeModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $IncomeModelCopyWith<IncomeModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $IncomeModelCopyWith<$Res> {
  factory $IncomeModelCopyWith(
    IncomeModel value,
    $Res Function(IncomeModel) then,
  ) = _$IncomeModelCopyWithImpl<$Res, IncomeModel>;
  @useResult
  $Res call({
    String uuid,
    int id,
    DateTime date,
    int initialMileage,
    int finalMileage,
    double initialGopay,
    double initialBca,
    double initialCash,
    double initialOvo,
    double initialBri,
    double initialRekpon,
    double finalGopay,
    double finalBca,
    double finalCash,
    double finalOvo,
    double finalBri,
    double finalRekpon,
    double initialCapital,
    double finalResult,
    DateTime createdAt,
    DateTime updatedAt,
    DateTime? deletedAt,
    String syncStatus,
  });
}

/// @nodoc
class _$IncomeModelCopyWithImpl<$Res, $Val extends IncomeModel>
    implements $IncomeModelCopyWith<$Res> {
  _$IncomeModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of IncomeModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? uuid = null,
    Object? id = null,
    Object? date = null,
    Object? initialMileage = null,
    Object? finalMileage = null,
    Object? initialGopay = null,
    Object? initialBca = null,
    Object? initialCash = null,
    Object? initialOvo = null,
    Object? initialBri = null,
    Object? initialRekpon = null,
    Object? finalGopay = null,
    Object? finalBca = null,
    Object? finalCash = null,
    Object? finalOvo = null,
    Object? finalBri = null,
    Object? finalRekpon = null,
    Object? initialCapital = null,
    Object? finalResult = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? deletedAt = freezed,
    Object? syncStatus = null,
  }) {
    return _then(
      _value.copyWith(
            uuid: null == uuid
                ? _value.uuid
                : uuid // ignore: cast_nullable_to_non_nullable
                      as String,
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as int,
            date: null == date
                ? _value.date
                : date // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            initialMileage: null == initialMileage
                ? _value.initialMileage
                : initialMileage // ignore: cast_nullable_to_non_nullable
                      as int,
            finalMileage: null == finalMileage
                ? _value.finalMileage
                : finalMileage // ignore: cast_nullable_to_non_nullable
                      as int,
            initialGopay: null == initialGopay
                ? _value.initialGopay
                : initialGopay // ignore: cast_nullable_to_non_nullable
                      as double,
            initialBca: null == initialBca
                ? _value.initialBca
                : initialBca // ignore: cast_nullable_to_non_nullable
                      as double,
            initialCash: null == initialCash
                ? _value.initialCash
                : initialCash // ignore: cast_nullable_to_non_nullable
                      as double,
            initialOvo: null == initialOvo
                ? _value.initialOvo
                : initialOvo // ignore: cast_nullable_to_non_nullable
                      as double,
            initialBri: null == initialBri
                ? _value.initialBri
                : initialBri // ignore: cast_nullable_to_non_nullable
                      as double,
            initialRekpon: null == initialRekpon
                ? _value.initialRekpon
                : initialRekpon // ignore: cast_nullable_to_non_nullable
                      as double,
            finalGopay: null == finalGopay
                ? _value.finalGopay
                : finalGopay // ignore: cast_nullable_to_non_nullable
                      as double,
            finalBca: null == finalBca
                ? _value.finalBca
                : finalBca // ignore: cast_nullable_to_non_nullable
                      as double,
            finalCash: null == finalCash
                ? _value.finalCash
                : finalCash // ignore: cast_nullable_to_non_nullable
                      as double,
            finalOvo: null == finalOvo
                ? _value.finalOvo
                : finalOvo // ignore: cast_nullable_to_non_nullable
                      as double,
            finalBri: null == finalBri
                ? _value.finalBri
                : finalBri // ignore: cast_nullable_to_non_nullable
                      as double,
            finalRekpon: null == finalRekpon
                ? _value.finalRekpon
                : finalRekpon // ignore: cast_nullable_to_non_nullable
                      as double,
            initialCapital: null == initialCapital
                ? _value.initialCapital
                : initialCapital // ignore: cast_nullable_to_non_nullable
                      as double,
            finalResult: null == finalResult
                ? _value.finalResult
                : finalResult // ignore: cast_nullable_to_non_nullable
                      as double,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            updatedAt: null == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            deletedAt: freezed == deletedAt
                ? _value.deletedAt
                : deletedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            syncStatus: null == syncStatus
                ? _value.syncStatus
                : syncStatus // ignore: cast_nullable_to_non_nullable
                      as String,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$IncomeModelImplCopyWith<$Res>
    implements $IncomeModelCopyWith<$Res> {
  factory _$$IncomeModelImplCopyWith(
    _$IncomeModelImpl value,
    $Res Function(_$IncomeModelImpl) then,
  ) = __$$IncomeModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String uuid,
    int id,
    DateTime date,
    int initialMileage,
    int finalMileage,
    double initialGopay,
    double initialBca,
    double initialCash,
    double initialOvo,
    double initialBri,
    double initialRekpon,
    double finalGopay,
    double finalBca,
    double finalCash,
    double finalOvo,
    double finalBri,
    double finalRekpon,
    double initialCapital,
    double finalResult,
    DateTime createdAt,
    DateTime updatedAt,
    DateTime? deletedAt,
    String syncStatus,
  });
}

/// @nodoc
class __$$IncomeModelImplCopyWithImpl<$Res>
    extends _$IncomeModelCopyWithImpl<$Res, _$IncomeModelImpl>
    implements _$$IncomeModelImplCopyWith<$Res> {
  __$$IncomeModelImplCopyWithImpl(
    _$IncomeModelImpl _value,
    $Res Function(_$IncomeModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of IncomeModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? uuid = null,
    Object? id = null,
    Object? date = null,
    Object? initialMileage = null,
    Object? finalMileage = null,
    Object? initialGopay = null,
    Object? initialBca = null,
    Object? initialCash = null,
    Object? initialOvo = null,
    Object? initialBri = null,
    Object? initialRekpon = null,
    Object? finalGopay = null,
    Object? finalBca = null,
    Object? finalCash = null,
    Object? finalOvo = null,
    Object? finalBri = null,
    Object? finalRekpon = null,
    Object? initialCapital = null,
    Object? finalResult = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? deletedAt = freezed,
    Object? syncStatus = null,
  }) {
    return _then(
      _$IncomeModelImpl(
        uuid: null == uuid
            ? _value.uuid
            : uuid // ignore: cast_nullable_to_non_nullable
                  as String,
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as int,
        date: null == date
            ? _value.date
            : date // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        initialMileage: null == initialMileage
            ? _value.initialMileage
            : initialMileage // ignore: cast_nullable_to_non_nullable
                  as int,
        finalMileage: null == finalMileage
            ? _value.finalMileage
            : finalMileage // ignore: cast_nullable_to_non_nullable
                  as int,
        initialGopay: null == initialGopay
            ? _value.initialGopay
            : initialGopay // ignore: cast_nullable_to_non_nullable
                  as double,
        initialBca: null == initialBca
            ? _value.initialBca
            : initialBca // ignore: cast_nullable_to_non_nullable
                  as double,
        initialCash: null == initialCash
            ? _value.initialCash
            : initialCash // ignore: cast_nullable_to_non_nullable
                  as double,
        initialOvo: null == initialOvo
            ? _value.initialOvo
            : initialOvo // ignore: cast_nullable_to_non_nullable
                  as double,
        initialBri: null == initialBri
            ? _value.initialBri
            : initialBri // ignore: cast_nullable_to_non_nullable
                  as double,
        initialRekpon: null == initialRekpon
            ? _value.initialRekpon
            : initialRekpon // ignore: cast_nullable_to_non_nullable
                  as double,
        finalGopay: null == finalGopay
            ? _value.finalGopay
            : finalGopay // ignore: cast_nullable_to_non_nullable
                  as double,
        finalBca: null == finalBca
            ? _value.finalBca
            : finalBca // ignore: cast_nullable_to_non_nullable
                  as double,
        finalCash: null == finalCash
            ? _value.finalCash
            : finalCash // ignore: cast_nullable_to_non_nullable
                  as double,
        finalOvo: null == finalOvo
            ? _value.finalOvo
            : finalOvo // ignore: cast_nullable_to_non_nullable
                  as double,
        finalBri: null == finalBri
            ? _value.finalBri
            : finalBri // ignore: cast_nullable_to_non_nullable
                  as double,
        finalRekpon: null == finalRekpon
            ? _value.finalRekpon
            : finalRekpon // ignore: cast_nullable_to_non_nullable
                  as double,
        initialCapital: null == initialCapital
            ? _value.initialCapital
            : initialCapital // ignore: cast_nullable_to_non_nullable
                  as double,
        finalResult: null == finalResult
            ? _value.finalResult
            : finalResult // ignore: cast_nullable_to_non_nullable
                  as double,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        updatedAt: null == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        deletedAt: freezed == deletedAt
            ? _value.deletedAt
            : deletedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        syncStatus: null == syncStatus
            ? _value.syncStatus
            : syncStatus // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$IncomeModelImpl extends _IncomeModel {
  const _$IncomeModelImpl({
    required this.uuid,
    required this.id,
    required this.date,
    required this.initialMileage,
    required this.finalMileage,
    required this.initialGopay,
    required this.initialBca,
    required this.initialCash,
    required this.initialOvo,
    required this.initialBri,
    required this.initialRekpon,
    required this.finalGopay,
    required this.finalBca,
    required this.finalCash,
    required this.finalOvo,
    required this.finalBri,
    required this.finalRekpon,
    required this.initialCapital,
    required this.finalResult,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
    this.syncStatus = 'pendingUpload',
  }) : super._();

  factory _$IncomeModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$IncomeModelImplFromJson(json);

  @override
  final String uuid;
  @override
  final int id;
  @override
  final DateTime date;
  @override
  final int initialMileage;
  @override
  final int finalMileage;
  @override
  final double initialGopay;
  @override
  final double initialBca;
  @override
  final double initialCash;
  @override
  final double initialOvo;
  @override
  final double initialBri;
  @override
  final double initialRekpon;
  @override
  final double finalGopay;
  @override
  final double finalBca;
  @override
  final double finalCash;
  @override
  final double finalOvo;
  @override
  final double finalBri;
  @override
  final double finalRekpon;
  @override
  final double initialCapital;
  @override
  final double finalResult;
  @override
  final DateTime createdAt;
  @override
  final DateTime updatedAt;
  @override
  final DateTime? deletedAt;
  @override
  @JsonKey()
  final String syncStatus;

  @override
  String toString() {
    return 'IncomeModel(uuid: $uuid, id: $id, date: $date, initialMileage: $initialMileage, finalMileage: $finalMileage, initialGopay: $initialGopay, initialBca: $initialBca, initialCash: $initialCash, initialOvo: $initialOvo, initialBri: $initialBri, initialRekpon: $initialRekpon, finalGopay: $finalGopay, finalBca: $finalBca, finalCash: $finalCash, finalOvo: $finalOvo, finalBri: $finalBri, finalRekpon: $finalRekpon, initialCapital: $initialCapital, finalResult: $finalResult, createdAt: $createdAt, updatedAt: $updatedAt, deletedAt: $deletedAt, syncStatus: $syncStatus)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$IncomeModelImpl &&
            (identical(other.uuid, uuid) || other.uuid == uuid) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.date, date) || other.date == date) &&
            (identical(other.initialMileage, initialMileage) ||
                other.initialMileage == initialMileage) &&
            (identical(other.finalMileage, finalMileage) ||
                other.finalMileage == finalMileage) &&
            (identical(other.initialGopay, initialGopay) ||
                other.initialGopay == initialGopay) &&
            (identical(other.initialBca, initialBca) ||
                other.initialBca == initialBca) &&
            (identical(other.initialCash, initialCash) ||
                other.initialCash == initialCash) &&
            (identical(other.initialOvo, initialOvo) ||
                other.initialOvo == initialOvo) &&
            (identical(other.initialBri, initialBri) ||
                other.initialBri == initialBri) &&
            (identical(other.initialRekpon, initialRekpon) ||
                other.initialRekpon == initialRekpon) &&
            (identical(other.finalGopay, finalGopay) ||
                other.finalGopay == finalGopay) &&
            (identical(other.finalBca, finalBca) ||
                other.finalBca == finalBca) &&
            (identical(other.finalCash, finalCash) ||
                other.finalCash == finalCash) &&
            (identical(other.finalOvo, finalOvo) ||
                other.finalOvo == finalOvo) &&
            (identical(other.finalBri, finalBri) ||
                other.finalBri == finalBri) &&
            (identical(other.finalRekpon, finalRekpon) ||
                other.finalRekpon == finalRekpon) &&
            (identical(other.initialCapital, initialCapital) ||
                other.initialCapital == initialCapital) &&
            (identical(other.finalResult, finalResult) ||
                other.finalResult == finalResult) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.deletedAt, deletedAt) ||
                other.deletedAt == deletedAt) &&
            (identical(other.syncStatus, syncStatus) ||
                other.syncStatus == syncStatus));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
    runtimeType,
    uuid,
    id,
    date,
    initialMileage,
    finalMileage,
    initialGopay,
    initialBca,
    initialCash,
    initialOvo,
    initialBri,
    initialRekpon,
    finalGopay,
    finalBca,
    finalCash,
    finalOvo,
    finalBri,
    finalRekpon,
    initialCapital,
    finalResult,
    createdAt,
    updatedAt,
    deletedAt,
    syncStatus,
  ]);

  /// Create a copy of IncomeModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$IncomeModelImplCopyWith<_$IncomeModelImpl> get copyWith =>
      __$$IncomeModelImplCopyWithImpl<_$IncomeModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$IncomeModelImplToJson(this);
  }
}

abstract class _IncomeModel extends IncomeModel {
  const factory _IncomeModel({
    required final String uuid,
    required final int id,
    required final DateTime date,
    required final int initialMileage,
    required final int finalMileage,
    required final double initialGopay,
    required final double initialBca,
    required final double initialCash,
    required final double initialOvo,
    required final double initialBri,
    required final double initialRekpon,
    required final double finalGopay,
    required final double finalBca,
    required final double finalCash,
    required final double finalOvo,
    required final double finalBri,
    required final double finalRekpon,
    required final double initialCapital,
    required final double finalResult,
    required final DateTime createdAt,
    required final DateTime updatedAt,
    final DateTime? deletedAt,
    final String syncStatus,
  }) = _$IncomeModelImpl;
  const _IncomeModel._() : super._();

  factory _IncomeModel.fromJson(Map<String, dynamic> json) =
      _$IncomeModelImpl.fromJson;

  @override
  String get uuid;
  @override
  int get id;
  @override
  DateTime get date;
  @override
  int get initialMileage;
  @override
  int get finalMileage;
  @override
  double get initialGopay;
  @override
  double get initialBca;
  @override
  double get initialCash;
  @override
  double get initialOvo;
  @override
  double get initialBri;
  @override
  double get initialRekpon;
  @override
  double get finalGopay;
  @override
  double get finalBca;
  @override
  double get finalCash;
  @override
  double get finalOvo;
  @override
  double get finalBri;
  @override
  double get finalRekpon;
  @override
  double get initialCapital;
  @override
  double get finalResult;
  @override
  DateTime get createdAt;
  @override
  DateTime get updatedAt;
  @override
  DateTime? get deletedAt;
  @override
  String get syncStatus;

  /// Create a copy of IncomeModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$IncomeModelImplCopyWith<_$IncomeModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CreateIncomeRequest _$CreateIncomeRequestFromJson(Map<String, dynamic> json) {
  return _CreateIncomeRequest.fromJson(json);
}

/// @nodoc
mixin _$CreateIncomeRequest {
  DateTime get date => throw _privateConstructorUsedError;
  int get initialMileage => throw _privateConstructorUsedError;
  int get finalMileage => throw _privateConstructorUsedError;
  double get initialGopay => throw _privateConstructorUsedError;
  double get initialBca => throw _privateConstructorUsedError;
  double get initialCash => throw _privateConstructorUsedError;
  double get initialOvo => throw _privateConstructorUsedError;
  double get initialBri => throw _privateConstructorUsedError;
  double get initialRekpon => throw _privateConstructorUsedError;
  double get finalGopay => throw _privateConstructorUsedError;
  double get finalBca => throw _privateConstructorUsedError;
  double get finalCash => throw _privateConstructorUsedError;
  double get finalOvo => throw _privateConstructorUsedError;
  double get finalBri => throw _privateConstructorUsedError;
  double get finalRekpon => throw _privateConstructorUsedError;
  double get initialCapital => throw _privateConstructorUsedError;
  double get finalResult => throw _privateConstructorUsedError;

  /// Serializes this CreateIncomeRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CreateIncomeRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CreateIncomeRequestCopyWith<CreateIncomeRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CreateIncomeRequestCopyWith<$Res> {
  factory $CreateIncomeRequestCopyWith(
    CreateIncomeRequest value,
    $Res Function(CreateIncomeRequest) then,
  ) = _$CreateIncomeRequestCopyWithImpl<$Res, CreateIncomeRequest>;
  @useResult
  $Res call({
    DateTime date,
    int initialMileage,
    int finalMileage,
    double initialGopay,
    double initialBca,
    double initialCash,
    double initialOvo,
    double initialBri,
    double initialRekpon,
    double finalGopay,
    double finalBca,
    double finalCash,
    double finalOvo,
    double finalBri,
    double finalRekpon,
    double initialCapital,
    double finalResult,
  });
}

/// @nodoc
class _$CreateIncomeRequestCopyWithImpl<$Res, $Val extends CreateIncomeRequest>
    implements $CreateIncomeRequestCopyWith<$Res> {
  _$CreateIncomeRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CreateIncomeRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? date = null,
    Object? initialMileage = null,
    Object? finalMileage = null,
    Object? initialGopay = null,
    Object? initialBca = null,
    Object? initialCash = null,
    Object? initialOvo = null,
    Object? initialBri = null,
    Object? initialRekpon = null,
    Object? finalGopay = null,
    Object? finalBca = null,
    Object? finalCash = null,
    Object? finalOvo = null,
    Object? finalBri = null,
    Object? finalRekpon = null,
    Object? initialCapital = null,
    Object? finalResult = null,
  }) {
    return _then(
      _value.copyWith(
            date: null == date
                ? _value.date
                : date // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            initialMileage: null == initialMileage
                ? _value.initialMileage
                : initialMileage // ignore: cast_nullable_to_non_nullable
                      as int,
            finalMileage: null == finalMileage
                ? _value.finalMileage
                : finalMileage // ignore: cast_nullable_to_non_nullable
                      as int,
            initialGopay: null == initialGopay
                ? _value.initialGopay
                : initialGopay // ignore: cast_nullable_to_non_nullable
                      as double,
            initialBca: null == initialBca
                ? _value.initialBca
                : initialBca // ignore: cast_nullable_to_non_nullable
                      as double,
            initialCash: null == initialCash
                ? _value.initialCash
                : initialCash // ignore: cast_nullable_to_non_nullable
                      as double,
            initialOvo: null == initialOvo
                ? _value.initialOvo
                : initialOvo // ignore: cast_nullable_to_non_nullable
                      as double,
            initialBri: null == initialBri
                ? _value.initialBri
                : initialBri // ignore: cast_nullable_to_non_nullable
                      as double,
            initialRekpon: null == initialRekpon
                ? _value.initialRekpon
                : initialRekpon // ignore: cast_nullable_to_non_nullable
                      as double,
            finalGopay: null == finalGopay
                ? _value.finalGopay
                : finalGopay // ignore: cast_nullable_to_non_nullable
                      as double,
            finalBca: null == finalBca
                ? _value.finalBca
                : finalBca // ignore: cast_nullable_to_non_nullable
                      as double,
            finalCash: null == finalCash
                ? _value.finalCash
                : finalCash // ignore: cast_nullable_to_non_nullable
                      as double,
            finalOvo: null == finalOvo
                ? _value.finalOvo
                : finalOvo // ignore: cast_nullable_to_non_nullable
                      as double,
            finalBri: null == finalBri
                ? _value.finalBri
                : finalBri // ignore: cast_nullable_to_non_nullable
                      as double,
            finalRekpon: null == finalRekpon
                ? _value.finalRekpon
                : finalRekpon // ignore: cast_nullable_to_non_nullable
                      as double,
            initialCapital: null == initialCapital
                ? _value.initialCapital
                : initialCapital // ignore: cast_nullable_to_non_nullable
                      as double,
            finalResult: null == finalResult
                ? _value.finalResult
                : finalResult // ignore: cast_nullable_to_non_nullable
                      as double,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$CreateIncomeRequestImplCopyWith<$Res>
    implements $CreateIncomeRequestCopyWith<$Res> {
  factory _$$CreateIncomeRequestImplCopyWith(
    _$CreateIncomeRequestImpl value,
    $Res Function(_$CreateIncomeRequestImpl) then,
  ) = __$$CreateIncomeRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    DateTime date,
    int initialMileage,
    int finalMileage,
    double initialGopay,
    double initialBca,
    double initialCash,
    double initialOvo,
    double initialBri,
    double initialRekpon,
    double finalGopay,
    double finalBca,
    double finalCash,
    double finalOvo,
    double finalBri,
    double finalRekpon,
    double initialCapital,
    double finalResult,
  });
}

/// @nodoc
class __$$CreateIncomeRequestImplCopyWithImpl<$Res>
    extends _$CreateIncomeRequestCopyWithImpl<$Res, _$CreateIncomeRequestImpl>
    implements _$$CreateIncomeRequestImplCopyWith<$Res> {
  __$$CreateIncomeRequestImplCopyWithImpl(
    _$CreateIncomeRequestImpl _value,
    $Res Function(_$CreateIncomeRequestImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of CreateIncomeRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? date = null,
    Object? initialMileage = null,
    Object? finalMileage = null,
    Object? initialGopay = null,
    Object? initialBca = null,
    Object? initialCash = null,
    Object? initialOvo = null,
    Object? initialBri = null,
    Object? initialRekpon = null,
    Object? finalGopay = null,
    Object? finalBca = null,
    Object? finalCash = null,
    Object? finalOvo = null,
    Object? finalBri = null,
    Object? finalRekpon = null,
    Object? initialCapital = null,
    Object? finalResult = null,
  }) {
    return _then(
      _$CreateIncomeRequestImpl(
        date: null == date
            ? _value.date
            : date // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        initialMileage: null == initialMileage
            ? _value.initialMileage
            : initialMileage // ignore: cast_nullable_to_non_nullable
                  as int,
        finalMileage: null == finalMileage
            ? _value.finalMileage
            : finalMileage // ignore: cast_nullable_to_non_nullable
                  as int,
        initialGopay: null == initialGopay
            ? _value.initialGopay
            : initialGopay // ignore: cast_nullable_to_non_nullable
                  as double,
        initialBca: null == initialBca
            ? _value.initialBca
            : initialBca // ignore: cast_nullable_to_non_nullable
                  as double,
        initialCash: null == initialCash
            ? _value.initialCash
            : initialCash // ignore: cast_nullable_to_non_nullable
                  as double,
        initialOvo: null == initialOvo
            ? _value.initialOvo
            : initialOvo // ignore: cast_nullable_to_non_nullable
                  as double,
        initialBri: null == initialBri
            ? _value.initialBri
            : initialBri // ignore: cast_nullable_to_non_nullable
                  as double,
        initialRekpon: null == initialRekpon
            ? _value.initialRekpon
            : initialRekpon // ignore: cast_nullable_to_non_nullable
                  as double,
        finalGopay: null == finalGopay
            ? _value.finalGopay
            : finalGopay // ignore: cast_nullable_to_non_nullable
                  as double,
        finalBca: null == finalBca
            ? _value.finalBca
            : finalBca // ignore: cast_nullable_to_non_nullable
                  as double,
        finalCash: null == finalCash
            ? _value.finalCash
            : finalCash // ignore: cast_nullable_to_non_nullable
                  as double,
        finalOvo: null == finalOvo
            ? _value.finalOvo
            : finalOvo // ignore: cast_nullable_to_non_nullable
                  as double,
        finalBri: null == finalBri
            ? _value.finalBri
            : finalBri // ignore: cast_nullable_to_non_nullable
                  as double,
        finalRekpon: null == finalRekpon
            ? _value.finalRekpon
            : finalRekpon // ignore: cast_nullable_to_non_nullable
                  as double,
        initialCapital: null == initialCapital
            ? _value.initialCapital
            : initialCapital // ignore: cast_nullable_to_non_nullable
                  as double,
        finalResult: null == finalResult
            ? _value.finalResult
            : finalResult // ignore: cast_nullable_to_non_nullable
                  as double,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$CreateIncomeRequestImpl extends _CreateIncomeRequest {
  const _$CreateIncomeRequestImpl({
    required this.date,
    required this.initialMileage,
    required this.finalMileage,
    required this.initialGopay,
    required this.initialBca,
    required this.initialCash,
    required this.initialOvo,
    required this.initialBri,
    required this.initialRekpon,
    required this.finalGopay,
    required this.finalBca,
    required this.finalCash,
    required this.finalOvo,
    required this.finalBri,
    required this.finalRekpon,
    required this.initialCapital,
    required this.finalResult,
  }) : super._();

  factory _$CreateIncomeRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$CreateIncomeRequestImplFromJson(json);

  @override
  final DateTime date;
  @override
  final int initialMileage;
  @override
  final int finalMileage;
  @override
  final double initialGopay;
  @override
  final double initialBca;
  @override
  final double initialCash;
  @override
  final double initialOvo;
  @override
  final double initialBri;
  @override
  final double initialRekpon;
  @override
  final double finalGopay;
  @override
  final double finalBca;
  @override
  final double finalCash;
  @override
  final double finalOvo;
  @override
  final double finalBri;
  @override
  final double finalRekpon;
  @override
  final double initialCapital;
  @override
  final double finalResult;

  @override
  String toString() {
    return 'CreateIncomeRequest(date: $date, initialMileage: $initialMileage, finalMileage: $finalMileage, initialGopay: $initialGopay, initialBca: $initialBca, initialCash: $initialCash, initialOvo: $initialOvo, initialBri: $initialBri, initialRekpon: $initialRekpon, finalGopay: $finalGopay, finalBca: $finalBca, finalCash: $finalCash, finalOvo: $finalOvo, finalBri: $finalBri, finalRekpon: $finalRekpon, initialCapital: $initialCapital, finalResult: $finalResult)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreateIncomeRequestImpl &&
            (identical(other.date, date) || other.date == date) &&
            (identical(other.initialMileage, initialMileage) ||
                other.initialMileage == initialMileage) &&
            (identical(other.finalMileage, finalMileage) ||
                other.finalMileage == finalMileage) &&
            (identical(other.initialGopay, initialGopay) ||
                other.initialGopay == initialGopay) &&
            (identical(other.initialBca, initialBca) ||
                other.initialBca == initialBca) &&
            (identical(other.initialCash, initialCash) ||
                other.initialCash == initialCash) &&
            (identical(other.initialOvo, initialOvo) ||
                other.initialOvo == initialOvo) &&
            (identical(other.initialBri, initialBri) ||
                other.initialBri == initialBri) &&
            (identical(other.initialRekpon, initialRekpon) ||
                other.initialRekpon == initialRekpon) &&
            (identical(other.finalGopay, finalGopay) ||
                other.finalGopay == finalGopay) &&
            (identical(other.finalBca, finalBca) ||
                other.finalBca == finalBca) &&
            (identical(other.finalCash, finalCash) ||
                other.finalCash == finalCash) &&
            (identical(other.finalOvo, finalOvo) ||
                other.finalOvo == finalOvo) &&
            (identical(other.finalBri, finalBri) ||
                other.finalBri == finalBri) &&
            (identical(other.finalRekpon, finalRekpon) ||
                other.finalRekpon == finalRekpon) &&
            (identical(other.initialCapital, initialCapital) ||
                other.initialCapital == initialCapital) &&
            (identical(other.finalResult, finalResult) ||
                other.finalResult == finalResult));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    date,
    initialMileage,
    finalMileage,
    initialGopay,
    initialBca,
    initialCash,
    initialOvo,
    initialBri,
    initialRekpon,
    finalGopay,
    finalBca,
    finalCash,
    finalOvo,
    finalBri,
    finalRekpon,
    initialCapital,
    finalResult,
  );

  /// Create a copy of CreateIncomeRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CreateIncomeRequestImplCopyWith<_$CreateIncomeRequestImpl> get copyWith =>
      __$$CreateIncomeRequestImplCopyWithImpl<_$CreateIncomeRequestImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$CreateIncomeRequestImplToJson(this);
  }
}

abstract class _CreateIncomeRequest extends CreateIncomeRequest {
  const factory _CreateIncomeRequest({
    required final DateTime date,
    required final int initialMileage,
    required final int finalMileage,
    required final double initialGopay,
    required final double initialBca,
    required final double initialCash,
    required final double initialOvo,
    required final double initialBri,
    required final double initialRekpon,
    required final double finalGopay,
    required final double finalBca,
    required final double finalCash,
    required final double finalOvo,
    required final double finalBri,
    required final double finalRekpon,
    required final double initialCapital,
    required final double finalResult,
  }) = _$CreateIncomeRequestImpl;
  const _CreateIncomeRequest._() : super._();

  factory _CreateIncomeRequest.fromJson(Map<String, dynamic> json) =
      _$CreateIncomeRequestImpl.fromJson;

  @override
  DateTime get date;
  @override
  int get initialMileage;
  @override
  int get finalMileage;
  @override
  double get initialGopay;
  @override
  double get initialBca;
  @override
  double get initialCash;
  @override
  double get initialOvo;
  @override
  double get initialBri;
  @override
  double get initialRekpon;
  @override
  double get finalGopay;
  @override
  double get finalBca;
  @override
  double get finalCash;
  @override
  double get finalOvo;
  @override
  double get finalBri;
  @override
  double get finalRekpon;
  @override
  double get initialCapital;
  @override
  double get finalResult;

  /// Create a copy of CreateIncomeRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CreateIncomeRequestImplCopyWith<_$CreateIncomeRequestImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
