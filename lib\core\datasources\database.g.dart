// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'database.dart';

// ignore_for_file: type=lint
class $IncomeTableTable extends IncomeTable
    with TableInfo<$IncomeTableTable, IncomeTableData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $IncomeTableTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _uuidMeta = const VerificationMeta('uuid');
  @override
  late final GeneratedColumn<String> uuid = GeneratedColumn<String>(
    'uuid',
    aliasedName,
    false,
    type: DriftSqlType.string,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
    'id',
    aliasedName,
    false,
    hasAutoIncrement: true,
    type: DriftSqlType.int,
    requiredDuringInsert: true,
    defaultConstraints: GeneratedColumn.constraintIsAlways(
      'PRIMARY KEY AUTOINCREMENT',
    ),
  );
  static const VerificationMeta _dateMeta = const VerificationMeta('date');
  @override
  late final GeneratedColumn<DateTime> date = GeneratedColumn<DateTime>(
    'date',
    aliasedName,
    false,
    type: DriftSqlType.dateTime,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _initialMileageMeta = const VerificationMeta(
    'initialMileage',
  );
  @override
  late final GeneratedColumn<int> initialMileage = GeneratedColumn<int>(
    'initial_mileage',
    aliasedName,
    false,
    type: DriftSqlType.int,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _finalMileageMeta = const VerificationMeta(
    'finalMileage',
  );
  @override
  late final GeneratedColumn<int> finalMileage = GeneratedColumn<int>(
    'final_mileage',
    aliasedName,
    false,
    type: DriftSqlType.int,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _initialGopayMeta = const VerificationMeta(
    'initialGopay',
  );
  @override
  late final GeneratedColumn<double> initialGopay = GeneratedColumn<double>(
    'initial_gopay',
    aliasedName,
    false,
    type: DriftSqlType.double,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _initialBcaMeta = const VerificationMeta(
    'initialBca',
  );
  @override
  late final GeneratedColumn<double> initialBca = GeneratedColumn<double>(
    'initial_bca',
    aliasedName,
    false,
    type: DriftSqlType.double,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _initialCashMeta = const VerificationMeta(
    'initialCash',
  );
  @override
  late final GeneratedColumn<double> initialCash = GeneratedColumn<double>(
    'initial_cash',
    aliasedName,
    false,
    type: DriftSqlType.double,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _initialOvoMeta = const VerificationMeta(
    'initialOvo',
  );
  @override
  late final GeneratedColumn<double> initialOvo = GeneratedColumn<double>(
    'initial_ovo',
    aliasedName,
    false,
    type: DriftSqlType.double,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _initialBriMeta = const VerificationMeta(
    'initialBri',
  );
  @override
  late final GeneratedColumn<double> initialBri = GeneratedColumn<double>(
    'initial_bri',
    aliasedName,
    false,
    type: DriftSqlType.double,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _initialRekponMeta = const VerificationMeta(
    'initialRekpon',
  );
  @override
  late final GeneratedColumn<double> initialRekpon = GeneratedColumn<double>(
    'initial_rekpon',
    aliasedName,
    false,
    type: DriftSqlType.double,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _finalGopayMeta = const VerificationMeta(
    'finalGopay',
  );
  @override
  late final GeneratedColumn<double> finalGopay = GeneratedColumn<double>(
    'final_gopay',
    aliasedName,
    false,
    type: DriftSqlType.double,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _finalBcaMeta = const VerificationMeta(
    'finalBca',
  );
  @override
  late final GeneratedColumn<double> finalBca = GeneratedColumn<double>(
    'final_bca',
    aliasedName,
    false,
    type: DriftSqlType.double,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _finalCashMeta = const VerificationMeta(
    'finalCash',
  );
  @override
  late final GeneratedColumn<double> finalCash = GeneratedColumn<double>(
    'final_cash',
    aliasedName,
    false,
    type: DriftSqlType.double,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _finalOvoMeta = const VerificationMeta(
    'finalOvo',
  );
  @override
  late final GeneratedColumn<double> finalOvo = GeneratedColumn<double>(
    'final_ovo',
    aliasedName,
    false,
    type: DriftSqlType.double,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _finalBriMeta = const VerificationMeta(
    'finalBri',
  );
  @override
  late final GeneratedColumn<double> finalBri = GeneratedColumn<double>(
    'final_bri',
    aliasedName,
    false,
    type: DriftSqlType.double,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _finalRekponMeta = const VerificationMeta(
    'finalRekpon',
  );
  @override
  late final GeneratedColumn<double> finalRekpon = GeneratedColumn<double>(
    'final_rekpon',
    aliasedName,
    false,
    type: DriftSqlType.double,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _initialCapitalMeta = const VerificationMeta(
    'initialCapital',
  );
  @override
  late final GeneratedColumn<double> initialCapital = GeneratedColumn<double>(
    'initial_capital',
    aliasedName,
    false,
    type: DriftSqlType.double,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _finalResultMeta = const VerificationMeta(
    'finalResult',
  );
  @override
  late final GeneratedColumn<double> finalResult = GeneratedColumn<double>(
    'final_result',
    aliasedName,
    false,
    type: DriftSqlType.double,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _mileageMeta = const VerificationMeta(
    'mileage',
  );
  @override
  late final GeneratedColumn<int> mileage = GeneratedColumn<int>(
    'mileage',
    aliasedName,
    false,
    generatedAs: GeneratedAs(finalMileage - initialMileage, true),
    type: DriftSqlType.int,
    requiredDuringInsert: false,
  );
  static const VerificationMeta _netIncomeMeta = const VerificationMeta(
    'netIncome',
  );
  @override
  late final GeneratedColumn<double> netIncome = GeneratedColumn<double>(
    'net_income',
    aliasedName,
    false,
    generatedAs: GeneratedAs(
      (finalGopay + finalBca + finalCash + finalOvo + finalBri + finalRekpon) -
          (initialGopay +
              initialBca +
              initialCash +
              initialOvo +
              initialBri +
              initialRekpon),
      true,
    ),
    type: DriftSqlType.double,
    requiredDuringInsert: false,
  );
  static const VerificationMeta _createdAtMeta = const VerificationMeta(
    'createdAt',
  );
  @override
  late final GeneratedColumn<DateTime> createdAt = GeneratedColumn<DateTime>(
    'created_at',
    aliasedName,
    false,
    type: DriftSqlType.dateTime,
    requiredDuringInsert: false,
    defaultValue: currentDateAndTime,
  );
  static const VerificationMeta _updatedAtMeta = const VerificationMeta(
    'updatedAt',
  );
  @override
  late final GeneratedColumn<DateTime> updatedAt = GeneratedColumn<DateTime>(
    'updated_at',
    aliasedName,
    false,
    type: DriftSqlType.dateTime,
    requiredDuringInsert: false,
    defaultValue: currentDateAndTime,
  );
  static const VerificationMeta _deletedAtMeta = const VerificationMeta(
    'deletedAt',
  );
  @override
  late final GeneratedColumn<DateTime> deletedAt = GeneratedColumn<DateTime>(
    'deleted_at',
    aliasedName,
    true,
    type: DriftSqlType.dateTime,
    requiredDuringInsert: false,
  );
  static const VerificationMeta _syncStatusMeta = const VerificationMeta(
    'syncStatus',
  );
  @override
  late final GeneratedColumn<String> syncStatus = GeneratedColumn<String>(
    'sync_status',
    aliasedName,
    false,
    type: DriftSqlType.string,
    requiredDuringInsert: false,
    defaultValue: const Constant('pendingUpload'),
  );
  @override
  List<GeneratedColumn> get $columns => [
    uuid,
    id,
    date,
    initialMileage,
    finalMileage,
    initialGopay,
    initialBca,
    initialCash,
    initialOvo,
    initialBri,
    initialRekpon,
    finalGopay,
    finalBca,
    finalCash,
    finalOvo,
    finalBri,
    finalRekpon,
    initialCapital,
    finalResult,
    mileage,
    netIncome,
    createdAt,
    updatedAt,
    deletedAt,
    syncStatus,
  ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'income_table';
  @override
  VerificationContext validateIntegrity(
    Insertable<IncomeTableData> instance, {
    bool isInserting = false,
  }) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('uuid')) {
      context.handle(
        _uuidMeta,
        uuid.isAcceptableOrUnknown(data['uuid']!, _uuidMeta),
      );
    } else if (isInserting) {
      context.missing(_uuidMeta);
    }
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    } else if (isInserting) {
      context.missing(_idMeta);
    }
    if (data.containsKey('date')) {
      context.handle(
        _dateMeta,
        date.isAcceptableOrUnknown(data['date']!, _dateMeta),
      );
    } else if (isInserting) {
      context.missing(_dateMeta);
    }
    if (data.containsKey('initial_mileage')) {
      context.handle(
        _initialMileageMeta,
        initialMileage.isAcceptableOrUnknown(
          data['initial_mileage']!,
          _initialMileageMeta,
        ),
      );
    } else if (isInserting) {
      context.missing(_initialMileageMeta);
    }
    if (data.containsKey('final_mileage')) {
      context.handle(
        _finalMileageMeta,
        finalMileage.isAcceptableOrUnknown(
          data['final_mileage']!,
          _finalMileageMeta,
        ),
      );
    } else if (isInserting) {
      context.missing(_finalMileageMeta);
    }
    if (data.containsKey('initial_gopay')) {
      context.handle(
        _initialGopayMeta,
        initialGopay.isAcceptableOrUnknown(
          data['initial_gopay']!,
          _initialGopayMeta,
        ),
      );
    } else if (isInserting) {
      context.missing(_initialGopayMeta);
    }
    if (data.containsKey('initial_bca')) {
      context.handle(
        _initialBcaMeta,
        initialBca.isAcceptableOrUnknown(data['initial_bca']!, _initialBcaMeta),
      );
    } else if (isInserting) {
      context.missing(_initialBcaMeta);
    }
    if (data.containsKey('initial_cash')) {
      context.handle(
        _initialCashMeta,
        initialCash.isAcceptableOrUnknown(
          data['initial_cash']!,
          _initialCashMeta,
        ),
      );
    } else if (isInserting) {
      context.missing(_initialCashMeta);
    }
    if (data.containsKey('initial_ovo')) {
      context.handle(
        _initialOvoMeta,
        initialOvo.isAcceptableOrUnknown(data['initial_ovo']!, _initialOvoMeta),
      );
    } else if (isInserting) {
      context.missing(_initialOvoMeta);
    }
    if (data.containsKey('initial_bri')) {
      context.handle(
        _initialBriMeta,
        initialBri.isAcceptableOrUnknown(data['initial_bri']!, _initialBriMeta),
      );
    } else if (isInserting) {
      context.missing(_initialBriMeta);
    }
    if (data.containsKey('initial_rekpon')) {
      context.handle(
        _initialRekponMeta,
        initialRekpon.isAcceptableOrUnknown(
          data['initial_rekpon']!,
          _initialRekponMeta,
        ),
      );
    } else if (isInserting) {
      context.missing(_initialRekponMeta);
    }
    if (data.containsKey('final_gopay')) {
      context.handle(
        _finalGopayMeta,
        finalGopay.isAcceptableOrUnknown(data['final_gopay']!, _finalGopayMeta),
      );
    } else if (isInserting) {
      context.missing(_finalGopayMeta);
    }
    if (data.containsKey('final_bca')) {
      context.handle(
        _finalBcaMeta,
        finalBca.isAcceptableOrUnknown(data['final_bca']!, _finalBcaMeta),
      );
    } else if (isInserting) {
      context.missing(_finalBcaMeta);
    }
    if (data.containsKey('final_cash')) {
      context.handle(
        _finalCashMeta,
        finalCash.isAcceptableOrUnknown(data['final_cash']!, _finalCashMeta),
      );
    } else if (isInserting) {
      context.missing(_finalCashMeta);
    }
    if (data.containsKey('final_ovo')) {
      context.handle(
        _finalOvoMeta,
        finalOvo.isAcceptableOrUnknown(data['final_ovo']!, _finalOvoMeta),
      );
    } else if (isInserting) {
      context.missing(_finalOvoMeta);
    }
    if (data.containsKey('final_bri')) {
      context.handle(
        _finalBriMeta,
        finalBri.isAcceptableOrUnknown(data['final_bri']!, _finalBriMeta),
      );
    } else if (isInserting) {
      context.missing(_finalBriMeta);
    }
    if (data.containsKey('final_rekpon')) {
      context.handle(
        _finalRekponMeta,
        finalRekpon.isAcceptableOrUnknown(
          data['final_rekpon']!,
          _finalRekponMeta,
        ),
      );
    } else if (isInserting) {
      context.missing(_finalRekponMeta);
    }
    if (data.containsKey('initial_capital')) {
      context.handle(
        _initialCapitalMeta,
        initialCapital.isAcceptableOrUnknown(
          data['initial_capital']!,
          _initialCapitalMeta,
        ),
      );
    } else if (isInserting) {
      context.missing(_initialCapitalMeta);
    }
    if (data.containsKey('final_result')) {
      context.handle(
        _finalResultMeta,
        finalResult.isAcceptableOrUnknown(
          data['final_result']!,
          _finalResultMeta,
        ),
      );
    } else if (isInserting) {
      context.missing(_finalResultMeta);
    }
    if (data.containsKey('mileage')) {
      context.handle(
        _mileageMeta,
        mileage.isAcceptableOrUnknown(data['mileage']!, _mileageMeta),
      );
    }
    if (data.containsKey('net_income')) {
      context.handle(
        _netIncomeMeta,
        netIncome.isAcceptableOrUnknown(data['net_income']!, _netIncomeMeta),
      );
    }
    if (data.containsKey('created_at')) {
      context.handle(
        _createdAtMeta,
        createdAt.isAcceptableOrUnknown(data['created_at']!, _createdAtMeta),
      );
    }
    if (data.containsKey('updated_at')) {
      context.handle(
        _updatedAtMeta,
        updatedAt.isAcceptableOrUnknown(data['updated_at']!, _updatedAtMeta),
      );
    }
    if (data.containsKey('deleted_at')) {
      context.handle(
        _deletedAtMeta,
        deletedAt.isAcceptableOrUnknown(data['deleted_at']!, _deletedAtMeta),
      );
    }
    if (data.containsKey('sync_status')) {
      context.handle(
        _syncStatusMeta,
        syncStatus.isAcceptableOrUnknown(data['sync_status']!, _syncStatusMeta),
      );
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {uuid};
  @override
  IncomeTableData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return IncomeTableData(
      uuid: attachedDatabase.typeMapping.read(
        DriftSqlType.string,
        data['${effectivePrefix}uuid'],
      )!,
      id: attachedDatabase.typeMapping.read(
        DriftSqlType.int,
        data['${effectivePrefix}id'],
      )!,
      date: attachedDatabase.typeMapping.read(
        DriftSqlType.dateTime,
        data['${effectivePrefix}date'],
      )!,
      initialMileage: attachedDatabase.typeMapping.read(
        DriftSqlType.int,
        data['${effectivePrefix}initial_mileage'],
      )!,
      finalMileage: attachedDatabase.typeMapping.read(
        DriftSqlType.int,
        data['${effectivePrefix}final_mileage'],
      )!,
      initialGopay: attachedDatabase.typeMapping.read(
        DriftSqlType.double,
        data['${effectivePrefix}initial_gopay'],
      )!,
      initialBca: attachedDatabase.typeMapping.read(
        DriftSqlType.double,
        data['${effectivePrefix}initial_bca'],
      )!,
      initialCash: attachedDatabase.typeMapping.read(
        DriftSqlType.double,
        data['${effectivePrefix}initial_cash'],
      )!,
      initialOvo: attachedDatabase.typeMapping.read(
        DriftSqlType.double,
        data['${effectivePrefix}initial_ovo'],
      )!,
      initialBri: attachedDatabase.typeMapping.read(
        DriftSqlType.double,
        data['${effectivePrefix}initial_bri'],
      )!,
      initialRekpon: attachedDatabase.typeMapping.read(
        DriftSqlType.double,
        data['${effectivePrefix}initial_rekpon'],
      )!,
      finalGopay: attachedDatabase.typeMapping.read(
        DriftSqlType.double,
        data['${effectivePrefix}final_gopay'],
      )!,
      finalBca: attachedDatabase.typeMapping.read(
        DriftSqlType.double,
        data['${effectivePrefix}final_bca'],
      )!,
      finalCash: attachedDatabase.typeMapping.read(
        DriftSqlType.double,
        data['${effectivePrefix}final_cash'],
      )!,
      finalOvo: attachedDatabase.typeMapping.read(
        DriftSqlType.double,
        data['${effectivePrefix}final_ovo'],
      )!,
      finalBri: attachedDatabase.typeMapping.read(
        DriftSqlType.double,
        data['${effectivePrefix}final_bri'],
      )!,
      finalRekpon: attachedDatabase.typeMapping.read(
        DriftSqlType.double,
        data['${effectivePrefix}final_rekpon'],
      )!,
      initialCapital: attachedDatabase.typeMapping.read(
        DriftSqlType.double,
        data['${effectivePrefix}initial_capital'],
      )!,
      finalResult: attachedDatabase.typeMapping.read(
        DriftSqlType.double,
        data['${effectivePrefix}final_result'],
      )!,
      mileage: attachedDatabase.typeMapping.read(
        DriftSqlType.int,
        data['${effectivePrefix}mileage'],
      )!,
      netIncome: attachedDatabase.typeMapping.read(
        DriftSqlType.double,
        data['${effectivePrefix}net_income'],
      )!,
      createdAt: attachedDatabase.typeMapping.read(
        DriftSqlType.dateTime,
        data['${effectivePrefix}created_at'],
      )!,
      updatedAt: attachedDatabase.typeMapping.read(
        DriftSqlType.dateTime,
        data['${effectivePrefix}updated_at'],
      )!,
      deletedAt: attachedDatabase.typeMapping.read(
        DriftSqlType.dateTime,
        data['${effectivePrefix}deleted_at'],
      ),
      syncStatus: attachedDatabase.typeMapping.read(
        DriftSqlType.string,
        data['${effectivePrefix}sync_status'],
      )!,
    );
  }

  @override
  $IncomeTableTable createAlias(String alias) {
    return $IncomeTableTable(attachedDatabase, alias);
  }
}

class IncomeTableData extends DataClass implements Insertable<IncomeTableData> {
  /// Primary key - UUID
  final String uuid;

  /// Auto-increment ID for internal use
  final int id;

  /// Date of the income record
  final DateTime date;

  /// Initial mileage reading
  final int initialMileage;

  /// Final mileage reading
  final int finalMileage;

  /// Initial GoPay balance
  final double initialGopay;

  /// Initial BCA balance
  final double initialBca;

  /// Initial Cash amount
  final double initialCash;

  /// Initial OVO balance
  final double initialOvo;

  /// Initial BRI balance
  final double initialBri;

  /// Initial Rekpon balance
  final double initialRekpon;

  /// Final GoPay balance
  final double finalGopay;

  /// Final BCA balance
  final double finalBca;

  /// Final Cash amount
  final double finalCash;

  /// Final OVO balance
  final double finalOvo;

  /// Final BRI balance
  final double finalBri;

  /// Final Rekpon balance
  final double finalRekpon;

  /// Initial capital amount
  final double initialCapital;

  /// Final result amount
  final double finalResult;

  /// Computed mileage (final - initial)
  final int mileage;

  /// Computed net income (total final - total initial)
  final double netIncome;

  /// Record creation timestamp
  final DateTime createdAt;

  /// Record update timestamp
  final DateTime updatedAt;

  /// Soft delete timestamp (null if not deleted)
  final DateTime? deletedAt;

  /// Sync status for cloud synchronization
  final String syncStatus;
  const IncomeTableData({
    required this.uuid,
    required this.id,
    required this.date,
    required this.initialMileage,
    required this.finalMileage,
    required this.initialGopay,
    required this.initialBca,
    required this.initialCash,
    required this.initialOvo,
    required this.initialBri,
    required this.initialRekpon,
    required this.finalGopay,
    required this.finalBca,
    required this.finalCash,
    required this.finalOvo,
    required this.finalBri,
    required this.finalRekpon,
    required this.initialCapital,
    required this.finalResult,
    required this.mileage,
    required this.netIncome,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
    required this.syncStatus,
  });
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['uuid'] = Variable<String>(uuid);
    map['id'] = Variable<int>(id);
    map['date'] = Variable<DateTime>(date);
    map['initial_mileage'] = Variable<int>(initialMileage);
    map['final_mileage'] = Variable<int>(finalMileage);
    map['initial_gopay'] = Variable<double>(initialGopay);
    map['initial_bca'] = Variable<double>(initialBca);
    map['initial_cash'] = Variable<double>(initialCash);
    map['initial_ovo'] = Variable<double>(initialOvo);
    map['initial_bri'] = Variable<double>(initialBri);
    map['initial_rekpon'] = Variable<double>(initialRekpon);
    map['final_gopay'] = Variable<double>(finalGopay);
    map['final_bca'] = Variable<double>(finalBca);
    map['final_cash'] = Variable<double>(finalCash);
    map['final_ovo'] = Variable<double>(finalOvo);
    map['final_bri'] = Variable<double>(finalBri);
    map['final_rekpon'] = Variable<double>(finalRekpon);
    map['initial_capital'] = Variable<double>(initialCapital);
    map['final_result'] = Variable<double>(finalResult);
    map['created_at'] = Variable<DateTime>(createdAt);
    map['updated_at'] = Variable<DateTime>(updatedAt);
    if (!nullToAbsent || deletedAt != null) {
      map['deleted_at'] = Variable<DateTime>(deletedAt);
    }
    map['sync_status'] = Variable<String>(syncStatus);
    return map;
  }

  IncomeTableCompanion toCompanion(bool nullToAbsent) {
    return IncomeTableCompanion(
      uuid: Value(uuid),
      id: Value(id),
      date: Value(date),
      initialMileage: Value(initialMileage),
      finalMileage: Value(finalMileage),
      initialGopay: Value(initialGopay),
      initialBca: Value(initialBca),
      initialCash: Value(initialCash),
      initialOvo: Value(initialOvo),
      initialBri: Value(initialBri),
      initialRekpon: Value(initialRekpon),
      finalGopay: Value(finalGopay),
      finalBca: Value(finalBca),
      finalCash: Value(finalCash),
      finalOvo: Value(finalOvo),
      finalBri: Value(finalBri),
      finalRekpon: Value(finalRekpon),
      initialCapital: Value(initialCapital),
      finalResult: Value(finalResult),
      createdAt: Value(createdAt),
      updatedAt: Value(updatedAt),
      deletedAt: deletedAt == null && nullToAbsent
          ? const Value.absent()
          : Value(deletedAt),
      syncStatus: Value(syncStatus),
    );
  }

  factory IncomeTableData.fromJson(
    Map<String, dynamic> json, {
    ValueSerializer? serializer,
  }) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return IncomeTableData(
      uuid: serializer.fromJson<String>(json['uuid']),
      id: serializer.fromJson<int>(json['id']),
      date: serializer.fromJson<DateTime>(json['date']),
      initialMileage: serializer.fromJson<int>(json['initialMileage']),
      finalMileage: serializer.fromJson<int>(json['finalMileage']),
      initialGopay: serializer.fromJson<double>(json['initialGopay']),
      initialBca: serializer.fromJson<double>(json['initialBca']),
      initialCash: serializer.fromJson<double>(json['initialCash']),
      initialOvo: serializer.fromJson<double>(json['initialOvo']),
      initialBri: serializer.fromJson<double>(json['initialBri']),
      initialRekpon: serializer.fromJson<double>(json['initialRekpon']),
      finalGopay: serializer.fromJson<double>(json['finalGopay']),
      finalBca: serializer.fromJson<double>(json['finalBca']),
      finalCash: serializer.fromJson<double>(json['finalCash']),
      finalOvo: serializer.fromJson<double>(json['finalOvo']),
      finalBri: serializer.fromJson<double>(json['finalBri']),
      finalRekpon: serializer.fromJson<double>(json['finalRekpon']),
      initialCapital: serializer.fromJson<double>(json['initialCapital']),
      finalResult: serializer.fromJson<double>(json['finalResult']),
      mileage: serializer.fromJson<int>(json['mileage']),
      netIncome: serializer.fromJson<double>(json['netIncome']),
      createdAt: serializer.fromJson<DateTime>(json['createdAt']),
      updatedAt: serializer.fromJson<DateTime>(json['updatedAt']),
      deletedAt: serializer.fromJson<DateTime?>(json['deletedAt']),
      syncStatus: serializer.fromJson<String>(json['syncStatus']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'uuid': serializer.toJson<String>(uuid),
      'id': serializer.toJson<int>(id),
      'date': serializer.toJson<DateTime>(date),
      'initialMileage': serializer.toJson<int>(initialMileage),
      'finalMileage': serializer.toJson<int>(finalMileage),
      'initialGopay': serializer.toJson<double>(initialGopay),
      'initialBca': serializer.toJson<double>(initialBca),
      'initialCash': serializer.toJson<double>(initialCash),
      'initialOvo': serializer.toJson<double>(initialOvo),
      'initialBri': serializer.toJson<double>(initialBri),
      'initialRekpon': serializer.toJson<double>(initialRekpon),
      'finalGopay': serializer.toJson<double>(finalGopay),
      'finalBca': serializer.toJson<double>(finalBca),
      'finalCash': serializer.toJson<double>(finalCash),
      'finalOvo': serializer.toJson<double>(finalOvo),
      'finalBri': serializer.toJson<double>(finalBri),
      'finalRekpon': serializer.toJson<double>(finalRekpon),
      'initialCapital': serializer.toJson<double>(initialCapital),
      'finalResult': serializer.toJson<double>(finalResult),
      'mileage': serializer.toJson<int>(mileage),
      'netIncome': serializer.toJson<double>(netIncome),
      'createdAt': serializer.toJson<DateTime>(createdAt),
      'updatedAt': serializer.toJson<DateTime>(updatedAt),
      'deletedAt': serializer.toJson<DateTime?>(deletedAt),
      'syncStatus': serializer.toJson<String>(syncStatus),
    };
  }

  IncomeTableData copyWith({
    String? uuid,
    int? id,
    DateTime? date,
    int? initialMileage,
    int? finalMileage,
    double? initialGopay,
    double? initialBca,
    double? initialCash,
    double? initialOvo,
    double? initialBri,
    double? initialRekpon,
    double? finalGopay,
    double? finalBca,
    double? finalCash,
    double? finalOvo,
    double? finalBri,
    double? finalRekpon,
    double? initialCapital,
    double? finalResult,
    int? mileage,
    double? netIncome,
    DateTime? createdAt,
    DateTime? updatedAt,
    Value<DateTime?> deletedAt = const Value.absent(),
    String? syncStatus,
  }) => IncomeTableData(
    uuid: uuid ?? this.uuid,
    id: id ?? this.id,
    date: date ?? this.date,
    initialMileage: initialMileage ?? this.initialMileage,
    finalMileage: finalMileage ?? this.finalMileage,
    initialGopay: initialGopay ?? this.initialGopay,
    initialBca: initialBca ?? this.initialBca,
    initialCash: initialCash ?? this.initialCash,
    initialOvo: initialOvo ?? this.initialOvo,
    initialBri: initialBri ?? this.initialBri,
    initialRekpon: initialRekpon ?? this.initialRekpon,
    finalGopay: finalGopay ?? this.finalGopay,
    finalBca: finalBca ?? this.finalBca,
    finalCash: finalCash ?? this.finalCash,
    finalOvo: finalOvo ?? this.finalOvo,
    finalBri: finalBri ?? this.finalBri,
    finalRekpon: finalRekpon ?? this.finalRekpon,
    initialCapital: initialCapital ?? this.initialCapital,
    finalResult: finalResult ?? this.finalResult,
    mileage: mileage ?? this.mileage,
    netIncome: netIncome ?? this.netIncome,
    createdAt: createdAt ?? this.createdAt,
    updatedAt: updatedAt ?? this.updatedAt,
    deletedAt: deletedAt.present ? deletedAt.value : this.deletedAt,
    syncStatus: syncStatus ?? this.syncStatus,
  );
  @override
  String toString() {
    return (StringBuffer('IncomeTableData(')
          ..write('uuid: $uuid, ')
          ..write('id: $id, ')
          ..write('date: $date, ')
          ..write('initialMileage: $initialMileage, ')
          ..write('finalMileage: $finalMileage, ')
          ..write('initialGopay: $initialGopay, ')
          ..write('initialBca: $initialBca, ')
          ..write('initialCash: $initialCash, ')
          ..write('initialOvo: $initialOvo, ')
          ..write('initialBri: $initialBri, ')
          ..write('initialRekpon: $initialRekpon, ')
          ..write('finalGopay: $finalGopay, ')
          ..write('finalBca: $finalBca, ')
          ..write('finalCash: $finalCash, ')
          ..write('finalOvo: $finalOvo, ')
          ..write('finalBri: $finalBri, ')
          ..write('finalRekpon: $finalRekpon, ')
          ..write('initialCapital: $initialCapital, ')
          ..write('finalResult: $finalResult, ')
          ..write('mileage: $mileage, ')
          ..write('netIncome: $netIncome, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt, ')
          ..write('deletedAt: $deletedAt, ')
          ..write('syncStatus: $syncStatus')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hashAll([
    uuid,
    id,
    date,
    initialMileage,
    finalMileage,
    initialGopay,
    initialBca,
    initialCash,
    initialOvo,
    initialBri,
    initialRekpon,
    finalGopay,
    finalBca,
    finalCash,
    finalOvo,
    finalBri,
    finalRekpon,
    initialCapital,
    finalResult,
    mileage,
    netIncome,
    createdAt,
    updatedAt,
    deletedAt,
    syncStatus,
  ]);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is IncomeTableData &&
          other.uuid == this.uuid &&
          other.id == this.id &&
          other.date == this.date &&
          other.initialMileage == this.initialMileage &&
          other.finalMileage == this.finalMileage &&
          other.initialGopay == this.initialGopay &&
          other.initialBca == this.initialBca &&
          other.initialCash == this.initialCash &&
          other.initialOvo == this.initialOvo &&
          other.initialBri == this.initialBri &&
          other.initialRekpon == this.initialRekpon &&
          other.finalGopay == this.finalGopay &&
          other.finalBca == this.finalBca &&
          other.finalCash == this.finalCash &&
          other.finalOvo == this.finalOvo &&
          other.finalBri == this.finalBri &&
          other.finalRekpon == this.finalRekpon &&
          other.initialCapital == this.initialCapital &&
          other.finalResult == this.finalResult &&
          other.mileage == this.mileage &&
          other.netIncome == this.netIncome &&
          other.createdAt == this.createdAt &&
          other.updatedAt == this.updatedAt &&
          other.deletedAt == this.deletedAt &&
          other.syncStatus == this.syncStatus);
}

class IncomeTableCompanion extends UpdateCompanion<IncomeTableData> {
  final Value<String> uuid;
  final Value<int> id;
  final Value<DateTime> date;
  final Value<int> initialMileage;
  final Value<int> finalMileage;
  final Value<double> initialGopay;
  final Value<double> initialBca;
  final Value<double> initialCash;
  final Value<double> initialOvo;
  final Value<double> initialBri;
  final Value<double> initialRekpon;
  final Value<double> finalGopay;
  final Value<double> finalBca;
  final Value<double> finalCash;
  final Value<double> finalOvo;
  final Value<double> finalBri;
  final Value<double> finalRekpon;
  final Value<double> initialCapital;
  final Value<double> finalResult;
  final Value<DateTime> createdAt;
  final Value<DateTime> updatedAt;
  final Value<DateTime?> deletedAt;
  final Value<String> syncStatus;
  final Value<int> rowid;
  const IncomeTableCompanion({
    this.uuid = const Value.absent(),
    this.id = const Value.absent(),
    this.date = const Value.absent(),
    this.initialMileage = const Value.absent(),
    this.finalMileage = const Value.absent(),
    this.initialGopay = const Value.absent(),
    this.initialBca = const Value.absent(),
    this.initialCash = const Value.absent(),
    this.initialOvo = const Value.absent(),
    this.initialBri = const Value.absent(),
    this.initialRekpon = const Value.absent(),
    this.finalGopay = const Value.absent(),
    this.finalBca = const Value.absent(),
    this.finalCash = const Value.absent(),
    this.finalOvo = const Value.absent(),
    this.finalBri = const Value.absent(),
    this.finalRekpon = const Value.absent(),
    this.initialCapital = const Value.absent(),
    this.finalResult = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.updatedAt = const Value.absent(),
    this.deletedAt = const Value.absent(),
    this.syncStatus = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  IncomeTableCompanion.insert({
    required String uuid,
    required int id,
    required DateTime date,
    required int initialMileage,
    required int finalMileage,
    required double initialGopay,
    required double initialBca,
    required double initialCash,
    required double initialOvo,
    required double initialBri,
    required double initialRekpon,
    required double finalGopay,
    required double finalBca,
    required double finalCash,
    required double finalOvo,
    required double finalBri,
    required double finalRekpon,
    required double initialCapital,
    required double finalResult,
    this.createdAt = const Value.absent(),
    this.updatedAt = const Value.absent(),
    this.deletedAt = const Value.absent(),
    this.syncStatus = const Value.absent(),
    this.rowid = const Value.absent(),
  }) : uuid = Value(uuid),
       id = Value(id),
       date = Value(date),
       initialMileage = Value(initialMileage),
       finalMileage = Value(finalMileage),
       initialGopay = Value(initialGopay),
       initialBca = Value(initialBca),
       initialCash = Value(initialCash),
       initialOvo = Value(initialOvo),
       initialBri = Value(initialBri),
       initialRekpon = Value(initialRekpon),
       finalGopay = Value(finalGopay),
       finalBca = Value(finalBca),
       finalCash = Value(finalCash),
       finalOvo = Value(finalOvo),
       finalBri = Value(finalBri),
       finalRekpon = Value(finalRekpon),
       initialCapital = Value(initialCapital),
       finalResult = Value(finalResult);
  static Insertable<IncomeTableData> custom({
    Expression<String>? uuid,
    Expression<int>? id,
    Expression<DateTime>? date,
    Expression<int>? initialMileage,
    Expression<int>? finalMileage,
    Expression<double>? initialGopay,
    Expression<double>? initialBca,
    Expression<double>? initialCash,
    Expression<double>? initialOvo,
    Expression<double>? initialBri,
    Expression<double>? initialRekpon,
    Expression<double>? finalGopay,
    Expression<double>? finalBca,
    Expression<double>? finalCash,
    Expression<double>? finalOvo,
    Expression<double>? finalBri,
    Expression<double>? finalRekpon,
    Expression<double>? initialCapital,
    Expression<double>? finalResult,
    Expression<DateTime>? createdAt,
    Expression<DateTime>? updatedAt,
    Expression<DateTime>? deletedAt,
    Expression<String>? syncStatus,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (uuid != null) 'uuid': uuid,
      if (id != null) 'id': id,
      if (date != null) 'date': date,
      if (initialMileage != null) 'initial_mileage': initialMileage,
      if (finalMileage != null) 'final_mileage': finalMileage,
      if (initialGopay != null) 'initial_gopay': initialGopay,
      if (initialBca != null) 'initial_bca': initialBca,
      if (initialCash != null) 'initial_cash': initialCash,
      if (initialOvo != null) 'initial_ovo': initialOvo,
      if (initialBri != null) 'initial_bri': initialBri,
      if (initialRekpon != null) 'initial_rekpon': initialRekpon,
      if (finalGopay != null) 'final_gopay': finalGopay,
      if (finalBca != null) 'final_bca': finalBca,
      if (finalCash != null) 'final_cash': finalCash,
      if (finalOvo != null) 'final_ovo': finalOvo,
      if (finalBri != null) 'final_bri': finalBri,
      if (finalRekpon != null) 'final_rekpon': finalRekpon,
      if (initialCapital != null) 'initial_capital': initialCapital,
      if (finalResult != null) 'final_result': finalResult,
      if (createdAt != null) 'created_at': createdAt,
      if (updatedAt != null) 'updated_at': updatedAt,
      if (deletedAt != null) 'deleted_at': deletedAt,
      if (syncStatus != null) 'sync_status': syncStatus,
      if (rowid != null) 'rowid': rowid,
    });
  }

  IncomeTableCompanion copyWith({
    Value<String>? uuid,
    Value<int>? id,
    Value<DateTime>? date,
    Value<int>? initialMileage,
    Value<int>? finalMileage,
    Value<double>? initialGopay,
    Value<double>? initialBca,
    Value<double>? initialCash,
    Value<double>? initialOvo,
    Value<double>? initialBri,
    Value<double>? initialRekpon,
    Value<double>? finalGopay,
    Value<double>? finalBca,
    Value<double>? finalCash,
    Value<double>? finalOvo,
    Value<double>? finalBri,
    Value<double>? finalRekpon,
    Value<double>? initialCapital,
    Value<double>? finalResult,
    Value<DateTime>? createdAt,
    Value<DateTime>? updatedAt,
    Value<DateTime?>? deletedAt,
    Value<String>? syncStatus,
    Value<int>? rowid,
  }) {
    return IncomeTableCompanion(
      uuid: uuid ?? this.uuid,
      id: id ?? this.id,
      date: date ?? this.date,
      initialMileage: initialMileage ?? this.initialMileage,
      finalMileage: finalMileage ?? this.finalMileage,
      initialGopay: initialGopay ?? this.initialGopay,
      initialBca: initialBca ?? this.initialBca,
      initialCash: initialCash ?? this.initialCash,
      initialOvo: initialOvo ?? this.initialOvo,
      initialBri: initialBri ?? this.initialBri,
      initialRekpon: initialRekpon ?? this.initialRekpon,
      finalGopay: finalGopay ?? this.finalGopay,
      finalBca: finalBca ?? this.finalBca,
      finalCash: finalCash ?? this.finalCash,
      finalOvo: finalOvo ?? this.finalOvo,
      finalBri: finalBri ?? this.finalBri,
      finalRekpon: finalRekpon ?? this.finalRekpon,
      initialCapital: initialCapital ?? this.initialCapital,
      finalResult: finalResult ?? this.finalResult,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      deletedAt: deletedAt ?? this.deletedAt,
      syncStatus: syncStatus ?? this.syncStatus,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (uuid.present) {
      map['uuid'] = Variable<String>(uuid.value);
    }
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (date.present) {
      map['date'] = Variable<DateTime>(date.value);
    }
    if (initialMileage.present) {
      map['initial_mileage'] = Variable<int>(initialMileage.value);
    }
    if (finalMileage.present) {
      map['final_mileage'] = Variable<int>(finalMileage.value);
    }
    if (initialGopay.present) {
      map['initial_gopay'] = Variable<double>(initialGopay.value);
    }
    if (initialBca.present) {
      map['initial_bca'] = Variable<double>(initialBca.value);
    }
    if (initialCash.present) {
      map['initial_cash'] = Variable<double>(initialCash.value);
    }
    if (initialOvo.present) {
      map['initial_ovo'] = Variable<double>(initialOvo.value);
    }
    if (initialBri.present) {
      map['initial_bri'] = Variable<double>(initialBri.value);
    }
    if (initialRekpon.present) {
      map['initial_rekpon'] = Variable<double>(initialRekpon.value);
    }
    if (finalGopay.present) {
      map['final_gopay'] = Variable<double>(finalGopay.value);
    }
    if (finalBca.present) {
      map['final_bca'] = Variable<double>(finalBca.value);
    }
    if (finalCash.present) {
      map['final_cash'] = Variable<double>(finalCash.value);
    }
    if (finalOvo.present) {
      map['final_ovo'] = Variable<double>(finalOvo.value);
    }
    if (finalBri.present) {
      map['final_bri'] = Variable<double>(finalBri.value);
    }
    if (finalRekpon.present) {
      map['final_rekpon'] = Variable<double>(finalRekpon.value);
    }
    if (initialCapital.present) {
      map['initial_capital'] = Variable<double>(initialCapital.value);
    }
    if (finalResult.present) {
      map['final_result'] = Variable<double>(finalResult.value);
    }
    if (createdAt.present) {
      map['created_at'] = Variable<DateTime>(createdAt.value);
    }
    if (updatedAt.present) {
      map['updated_at'] = Variable<DateTime>(updatedAt.value);
    }
    if (deletedAt.present) {
      map['deleted_at'] = Variable<DateTime>(deletedAt.value);
    }
    if (syncStatus.present) {
      map['sync_status'] = Variable<String>(syncStatus.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('IncomeTableCompanion(')
          ..write('uuid: $uuid, ')
          ..write('id: $id, ')
          ..write('date: $date, ')
          ..write('initialMileage: $initialMileage, ')
          ..write('finalMileage: $finalMileage, ')
          ..write('initialGopay: $initialGopay, ')
          ..write('initialBca: $initialBca, ')
          ..write('initialCash: $initialCash, ')
          ..write('initialOvo: $initialOvo, ')
          ..write('initialBri: $initialBri, ')
          ..write('initialRekpon: $initialRekpon, ')
          ..write('finalGopay: $finalGopay, ')
          ..write('finalBca: $finalBca, ')
          ..write('finalCash: $finalCash, ')
          ..write('finalOvo: $finalOvo, ')
          ..write('finalBri: $finalBri, ')
          ..write('finalRekpon: $finalRekpon, ')
          ..write('initialCapital: $initialCapital, ')
          ..write('finalResult: $finalResult, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt, ')
          ..write('deletedAt: $deletedAt, ')
          ..write('syncStatus: $syncStatus, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

abstract class _$AppDatabase extends GeneratedDatabase {
  _$AppDatabase(QueryExecutor e) : super(e);
  $AppDatabaseManager get managers => $AppDatabaseManager(this);
  late final $IncomeTableTable incomeTable = $IncomeTableTable(this);
  @override
  Iterable<TableInfo<Table, Object?>> get allTables =>
      allSchemaEntities.whereType<TableInfo<Table, Object?>>();
  @override
  List<DatabaseSchemaEntity> get allSchemaEntities => [incomeTable];
}

typedef $$IncomeTableTableCreateCompanionBuilder =
    IncomeTableCompanion Function({
      required String uuid,
      required int id,
      required DateTime date,
      required int initialMileage,
      required int finalMileage,
      required double initialGopay,
      required double initialBca,
      required double initialCash,
      required double initialOvo,
      required double initialBri,
      required double initialRekpon,
      required double finalGopay,
      required double finalBca,
      required double finalCash,
      required double finalOvo,
      required double finalBri,
      required double finalRekpon,
      required double initialCapital,
      required double finalResult,
      Value<DateTime> createdAt,
      Value<DateTime> updatedAt,
      Value<DateTime?> deletedAt,
      Value<String> syncStatus,
      Value<int> rowid,
    });
typedef $$IncomeTableTableUpdateCompanionBuilder =
    IncomeTableCompanion Function({
      Value<String> uuid,
      Value<int> id,
      Value<DateTime> date,
      Value<int> initialMileage,
      Value<int> finalMileage,
      Value<double> initialGopay,
      Value<double> initialBca,
      Value<double> initialCash,
      Value<double> initialOvo,
      Value<double> initialBri,
      Value<double> initialRekpon,
      Value<double> finalGopay,
      Value<double> finalBca,
      Value<double> finalCash,
      Value<double> finalOvo,
      Value<double> finalBri,
      Value<double> finalRekpon,
      Value<double> initialCapital,
      Value<double> finalResult,
      Value<DateTime> createdAt,
      Value<DateTime> updatedAt,
      Value<DateTime?> deletedAt,
      Value<String> syncStatus,
      Value<int> rowid,
    });

class $$IncomeTableTableFilterComposer
    extends Composer<_$AppDatabase, $IncomeTableTable> {
  $$IncomeTableTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<String> get uuid => $composableBuilder(
    column: $table.uuid,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<int> get id => $composableBuilder(
    column: $table.id,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<DateTime> get date => $composableBuilder(
    column: $table.date,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<int> get initialMileage => $composableBuilder(
    column: $table.initialMileage,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<int> get finalMileage => $composableBuilder(
    column: $table.finalMileage,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<double> get initialGopay => $composableBuilder(
    column: $table.initialGopay,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<double> get initialBca => $composableBuilder(
    column: $table.initialBca,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<double> get initialCash => $composableBuilder(
    column: $table.initialCash,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<double> get initialOvo => $composableBuilder(
    column: $table.initialOvo,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<double> get initialBri => $composableBuilder(
    column: $table.initialBri,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<double> get initialRekpon => $composableBuilder(
    column: $table.initialRekpon,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<double> get finalGopay => $composableBuilder(
    column: $table.finalGopay,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<double> get finalBca => $composableBuilder(
    column: $table.finalBca,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<double> get finalCash => $composableBuilder(
    column: $table.finalCash,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<double> get finalOvo => $composableBuilder(
    column: $table.finalOvo,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<double> get finalBri => $composableBuilder(
    column: $table.finalBri,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<double> get finalRekpon => $composableBuilder(
    column: $table.finalRekpon,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<double> get initialCapital => $composableBuilder(
    column: $table.initialCapital,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<double> get finalResult => $composableBuilder(
    column: $table.finalResult,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<int> get mileage => $composableBuilder(
    column: $table.mileage,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<double> get netIncome => $composableBuilder(
    column: $table.netIncome,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<DateTime> get createdAt => $composableBuilder(
    column: $table.createdAt,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<DateTime> get updatedAt => $composableBuilder(
    column: $table.updatedAt,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<DateTime> get deletedAt => $composableBuilder(
    column: $table.deletedAt,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get syncStatus => $composableBuilder(
    column: $table.syncStatus,
    builder: (column) => ColumnFilters(column),
  );
}

class $$IncomeTableTableOrderingComposer
    extends Composer<_$AppDatabase, $IncomeTableTable> {
  $$IncomeTableTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<String> get uuid => $composableBuilder(
    column: $table.uuid,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<int> get id => $composableBuilder(
    column: $table.id,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<DateTime> get date => $composableBuilder(
    column: $table.date,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<int> get initialMileage => $composableBuilder(
    column: $table.initialMileage,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<int> get finalMileage => $composableBuilder(
    column: $table.finalMileage,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<double> get initialGopay => $composableBuilder(
    column: $table.initialGopay,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<double> get initialBca => $composableBuilder(
    column: $table.initialBca,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<double> get initialCash => $composableBuilder(
    column: $table.initialCash,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<double> get initialOvo => $composableBuilder(
    column: $table.initialOvo,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<double> get initialBri => $composableBuilder(
    column: $table.initialBri,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<double> get initialRekpon => $composableBuilder(
    column: $table.initialRekpon,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<double> get finalGopay => $composableBuilder(
    column: $table.finalGopay,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<double> get finalBca => $composableBuilder(
    column: $table.finalBca,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<double> get finalCash => $composableBuilder(
    column: $table.finalCash,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<double> get finalOvo => $composableBuilder(
    column: $table.finalOvo,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<double> get finalBri => $composableBuilder(
    column: $table.finalBri,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<double> get finalRekpon => $composableBuilder(
    column: $table.finalRekpon,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<double> get initialCapital => $composableBuilder(
    column: $table.initialCapital,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<double> get finalResult => $composableBuilder(
    column: $table.finalResult,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<int> get mileage => $composableBuilder(
    column: $table.mileage,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<double> get netIncome => $composableBuilder(
    column: $table.netIncome,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<DateTime> get createdAt => $composableBuilder(
    column: $table.createdAt,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<DateTime> get updatedAt => $composableBuilder(
    column: $table.updatedAt,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<DateTime> get deletedAt => $composableBuilder(
    column: $table.deletedAt,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get syncStatus => $composableBuilder(
    column: $table.syncStatus,
    builder: (column) => ColumnOrderings(column),
  );
}

class $$IncomeTableTableAnnotationComposer
    extends Composer<_$AppDatabase, $IncomeTableTable> {
  $$IncomeTableTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<String> get uuid =>
      $composableBuilder(column: $table.uuid, builder: (column) => column);

  GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<DateTime> get date =>
      $composableBuilder(column: $table.date, builder: (column) => column);

  GeneratedColumn<int> get initialMileage => $composableBuilder(
    column: $table.initialMileage,
    builder: (column) => column,
  );

  GeneratedColumn<int> get finalMileage => $composableBuilder(
    column: $table.finalMileage,
    builder: (column) => column,
  );

  GeneratedColumn<double> get initialGopay => $composableBuilder(
    column: $table.initialGopay,
    builder: (column) => column,
  );

  GeneratedColumn<double> get initialBca => $composableBuilder(
    column: $table.initialBca,
    builder: (column) => column,
  );

  GeneratedColumn<double> get initialCash => $composableBuilder(
    column: $table.initialCash,
    builder: (column) => column,
  );

  GeneratedColumn<double> get initialOvo => $composableBuilder(
    column: $table.initialOvo,
    builder: (column) => column,
  );

  GeneratedColumn<double> get initialBri => $composableBuilder(
    column: $table.initialBri,
    builder: (column) => column,
  );

  GeneratedColumn<double> get initialRekpon => $composableBuilder(
    column: $table.initialRekpon,
    builder: (column) => column,
  );

  GeneratedColumn<double> get finalGopay => $composableBuilder(
    column: $table.finalGopay,
    builder: (column) => column,
  );

  GeneratedColumn<double> get finalBca =>
      $composableBuilder(column: $table.finalBca, builder: (column) => column);

  GeneratedColumn<double> get finalCash =>
      $composableBuilder(column: $table.finalCash, builder: (column) => column);

  GeneratedColumn<double> get finalOvo =>
      $composableBuilder(column: $table.finalOvo, builder: (column) => column);

  GeneratedColumn<double> get finalBri =>
      $composableBuilder(column: $table.finalBri, builder: (column) => column);

  GeneratedColumn<double> get finalRekpon => $composableBuilder(
    column: $table.finalRekpon,
    builder: (column) => column,
  );

  GeneratedColumn<double> get initialCapital => $composableBuilder(
    column: $table.initialCapital,
    builder: (column) => column,
  );

  GeneratedColumn<double> get finalResult => $composableBuilder(
    column: $table.finalResult,
    builder: (column) => column,
  );

  GeneratedColumn<int> get mileage =>
      $composableBuilder(column: $table.mileage, builder: (column) => column);

  GeneratedColumn<double> get netIncome =>
      $composableBuilder(column: $table.netIncome, builder: (column) => column);

  GeneratedColumn<DateTime> get createdAt =>
      $composableBuilder(column: $table.createdAt, builder: (column) => column);

  GeneratedColumn<DateTime> get updatedAt =>
      $composableBuilder(column: $table.updatedAt, builder: (column) => column);

  GeneratedColumn<DateTime> get deletedAt =>
      $composableBuilder(column: $table.deletedAt, builder: (column) => column);

  GeneratedColumn<String> get syncStatus => $composableBuilder(
    column: $table.syncStatus,
    builder: (column) => column,
  );
}

class $$IncomeTableTableTableManager
    extends
        RootTableManager<
          _$AppDatabase,
          $IncomeTableTable,
          IncomeTableData,
          $$IncomeTableTableFilterComposer,
          $$IncomeTableTableOrderingComposer,
          $$IncomeTableTableAnnotationComposer,
          $$IncomeTableTableCreateCompanionBuilder,
          $$IncomeTableTableUpdateCompanionBuilder,
          (
            IncomeTableData,
            BaseReferences<_$AppDatabase, $IncomeTableTable, IncomeTableData>,
          ),
          IncomeTableData,
          PrefetchHooks Function()
        > {
  $$IncomeTableTableTableManager(_$AppDatabase db, $IncomeTableTable table)
    : super(
        TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$IncomeTableTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$IncomeTableTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$IncomeTableTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback:
              ({
                Value<String> uuid = const Value.absent(),
                Value<int> id = const Value.absent(),
                Value<DateTime> date = const Value.absent(),
                Value<int> initialMileage = const Value.absent(),
                Value<int> finalMileage = const Value.absent(),
                Value<double> initialGopay = const Value.absent(),
                Value<double> initialBca = const Value.absent(),
                Value<double> initialCash = const Value.absent(),
                Value<double> initialOvo = const Value.absent(),
                Value<double> initialBri = const Value.absent(),
                Value<double> initialRekpon = const Value.absent(),
                Value<double> finalGopay = const Value.absent(),
                Value<double> finalBca = const Value.absent(),
                Value<double> finalCash = const Value.absent(),
                Value<double> finalOvo = const Value.absent(),
                Value<double> finalBri = const Value.absent(),
                Value<double> finalRekpon = const Value.absent(),
                Value<double> initialCapital = const Value.absent(),
                Value<double> finalResult = const Value.absent(),
                Value<DateTime> createdAt = const Value.absent(),
                Value<DateTime> updatedAt = const Value.absent(),
                Value<DateTime?> deletedAt = const Value.absent(),
                Value<String> syncStatus = const Value.absent(),
                Value<int> rowid = const Value.absent(),
              }) => IncomeTableCompanion(
                uuid: uuid,
                id: id,
                date: date,
                initialMileage: initialMileage,
                finalMileage: finalMileage,
                initialGopay: initialGopay,
                initialBca: initialBca,
                initialCash: initialCash,
                initialOvo: initialOvo,
                initialBri: initialBri,
                initialRekpon: initialRekpon,
                finalGopay: finalGopay,
                finalBca: finalBca,
                finalCash: finalCash,
                finalOvo: finalOvo,
                finalBri: finalBri,
                finalRekpon: finalRekpon,
                initialCapital: initialCapital,
                finalResult: finalResult,
                createdAt: createdAt,
                updatedAt: updatedAt,
                deletedAt: deletedAt,
                syncStatus: syncStatus,
                rowid: rowid,
              ),
          createCompanionCallback:
              ({
                required String uuid,
                required int id,
                required DateTime date,
                required int initialMileage,
                required int finalMileage,
                required double initialGopay,
                required double initialBca,
                required double initialCash,
                required double initialOvo,
                required double initialBri,
                required double initialRekpon,
                required double finalGopay,
                required double finalBca,
                required double finalCash,
                required double finalOvo,
                required double finalBri,
                required double finalRekpon,
                required double initialCapital,
                required double finalResult,
                Value<DateTime> createdAt = const Value.absent(),
                Value<DateTime> updatedAt = const Value.absent(),
                Value<DateTime?> deletedAt = const Value.absent(),
                Value<String> syncStatus = const Value.absent(),
                Value<int> rowid = const Value.absent(),
              }) => IncomeTableCompanion.insert(
                uuid: uuid,
                id: id,
                date: date,
                initialMileage: initialMileage,
                finalMileage: finalMileage,
                initialGopay: initialGopay,
                initialBca: initialBca,
                initialCash: initialCash,
                initialOvo: initialOvo,
                initialBri: initialBri,
                initialRekpon: initialRekpon,
                finalGopay: finalGopay,
                finalBca: finalBca,
                finalCash: finalCash,
                finalOvo: finalOvo,
                finalBri: finalBri,
                finalRekpon: finalRekpon,
                initialCapital: initialCapital,
                finalResult: finalResult,
                createdAt: createdAt,
                updatedAt: updatedAt,
                deletedAt: deletedAt,
                syncStatus: syncStatus,
                rowid: rowid,
              ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ),
      );
}

typedef $$IncomeTableTableProcessedTableManager =
    ProcessedTableManager<
      _$AppDatabase,
      $IncomeTableTable,
      IncomeTableData,
      $$IncomeTableTableFilterComposer,
      $$IncomeTableTableOrderingComposer,
      $$IncomeTableTableAnnotationComposer,
      $$IncomeTableTableCreateCompanionBuilder,
      $$IncomeTableTableUpdateCompanionBuilder,
      (
        IncomeTableData,
        BaseReferences<_$AppDatabase, $IncomeTableTable, IncomeTableData>,
      ),
      IncomeTableData,
      PrefetchHooks Function()
    >;

class $AppDatabaseManager {
  final _$AppDatabase _db;
  $AppDatabaseManager(this._db);
  $$IncomeTableTableTableManager get incomeTable =>
      $$IncomeTableTableTableManager(_db, _db.incomeTable);
}
