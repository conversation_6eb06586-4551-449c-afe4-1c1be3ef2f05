import '../entities/income_entity.dart';
import '../repositories/income_repository.dart';

/// Use case for updating an existing income record
/// 
/// This use case handles the business logic for updating income records,
/// including validation and data processing.
class UpdateIncomeUseCase {
  final IncomeRepository _repository;

  UpdateIncomeUseCase(this._repository);

  /// Execute the use case
  /// 
  /// Updates an existing income record with the provided data.
  /// Validates the input and applies business rules before saving.
  Future<IncomeEntity> execute(UpdateIncomeParams params) async {
    // Validate input parameters
    _validateParams(params);

    // Get existing income record
    final existingIncome = await _repository.getIncomeByUuid(params.uuid);
    if (existingIncome == null) {
      throw ArgumentError('Income record not found: ${params.uuid}');
    }

    // Create updated income entity
    final updatedIncome = existingIncome.copyWith(
      date: params.date,
      initialGopay: params.initialGopay,
      finalGopay: params.finalGopay,
      initialBca: params.initialBca,
      finalBca: params.finalBca,
      initialCash: params.initialCash,
      finalCash: params.finalCash,
      initialOvo: params.initialOvo,
      finalOvo: params.finalOvo,
      initialBri: params.initialBri,
      finalBri: params.finalBri,
      initialRekpon: params.initialRekpon,
      finalRekpon: params.finalRekpon,
      initialCapital: params.initialCapital,
      finalResult: params.finalResult,
      updatedAt: DateTime.now(),
      syncStatus: 'pendingUpload',
    );

    // Validate business rules
    _validateBusinessRules(updatedIncome);

    // Save to repository
    return await _repository.updateIncome(updatedIncome);
  }

  /// Validate input parameters
  void _validateParams(UpdateIncomeParams params) {
    if (params.uuid.isEmpty) {
      throw ArgumentError('Income UUID cannot be empty');
    }

    if (params.date.isAfter(DateTime.now())) {
      throw ArgumentError('Income date cannot be in the future');
    }

    if (params.initialCapital < 0) {
      throw ArgumentError('Initial capital cannot be negative');
    }

    if (params.finalResult < 0) {
      throw ArgumentError('Final result cannot be negative');
    }

    // Validate payment method amounts
    if (params.initialGopay < 0 || params.finalGopay < 0) {
      throw ArgumentError('GoPay amounts cannot be negative');
    }

    if (params.initialBca < 0 || params.finalBca < 0) {
      throw ArgumentError('BCA amounts cannot be negative');
    }

    if (params.initialCash < 0 || params.finalCash < 0) {
      throw ArgumentError('Cash amounts cannot be negative');
    }

    if (params.initialOvo < 0 || params.finalOvo < 0) {
      throw ArgumentError('OVO amounts cannot be negative');
    }

    if (params.initialBri < 0 || params.finalBri < 0) {
      throw ArgumentError('BRI amounts cannot be negative');
    }

    if (params.initialRekpon < 0 || params.finalRekpon < 0) {
      throw ArgumentError('Rekpon amounts cannot be negative');
    }
  }

  /// Validate business rules
  void _validateBusinessRules(IncomeEntity income) {
    // Check if total initial amounts match initial capital
    final totalInitial = income.totalInitialAmount;
    if ((totalInitial - income.initialCapital).abs() > 0.01) {
      throw ArgumentError(
        'Total initial payment amounts (${totalInitial.toStringAsFixed(2)}) '
        'must equal initial capital (${income.initialCapital.toStringAsFixed(2)})'
      );
    }

    // Check if total final amounts match final result
    final totalFinal = income.totalFinalAmount;
    if ((totalFinal - income.finalResult).abs() > 0.01) {
      throw ArgumentError(
        'Total final payment amounts (${totalFinal.toStringAsFixed(2)}) '
        'must equal final result (${income.finalResult.toStringAsFixed(2)})'
      );
    }

    // Validate that net income is reasonable
    if (income.netIncome < -income.initialCapital) {
      throw ArgumentError(
        'Net income cannot be less than negative initial capital'
      );
    }

    // Check for suspicious mileage (if calculated mileage is unreasonably high)
    if (income.mileage > 1000) {
      // This is a warning, not an error - log it but don't throw
      print('Warning: High mileage detected (${income.mileage} km) for income record');
    }
  }
}

/// Parameters for updating income
class UpdateIncomeParams {
  final String uuid;
  final DateTime date;
  final double initialGopay;
  final double finalGopay;
  final double initialBca;
  final double finalBca;
  final double initialCash;
  final double finalCash;
  final double initialOvo;
  final double finalOvo;
  final double initialBri;
  final double finalBri;
  final double initialRekpon;
  final double finalRekpon;
  final double initialCapital;
  final double finalResult;

  const UpdateIncomeParams({
    required this.uuid,
    required this.date,
    required this.initialGopay,
    required this.finalGopay,
    required this.initialBca,
    required this.finalBca,
    required this.initialCash,
    required this.finalCash,
    required this.initialOvo,
    required this.finalOvo,
    required this.initialBri,
    required this.finalBri,
    required this.initialRekpon,
    required this.finalRekpon,
    required this.initialCapital,
    required this.finalResult,
  });

  /// Create from existing income entity
  factory UpdateIncomeParams.fromEntity(IncomeEntity entity) {
    return UpdateIncomeParams(
      uuid: entity.uuid,
      date: entity.date,
      initialGopay: entity.initialGopay,
      finalGopay: entity.finalGopay,
      initialBca: entity.initialBca,
      finalBca: entity.finalBca,
      initialCash: entity.initialCash,
      finalCash: entity.finalCash,
      initialOvo: entity.initialOvo,
      finalOvo: entity.finalOvo,
      initialBri: entity.initialBri,
      finalBri: entity.finalBri,
      initialRekpon: entity.initialRekpon,
      finalRekpon: entity.finalRekpon,
      initialCapital: entity.initialCapital,
      finalResult: entity.finalResult,
    );
  }

  /// Create from map (useful for form data)
  factory UpdateIncomeParams.fromMap(Map<String, dynamic> map) {
    return UpdateIncomeParams(
      uuid: map['uuid'] as String,
      date: map['date'] as DateTime,
      initialGopay: (map['initialGopay'] as num).toDouble(),
      finalGopay: (map['finalGopay'] as num).toDouble(),
      initialBca: (map['initialBca'] as num).toDouble(),
      finalBca: (map['finalBca'] as num).toDouble(),
      initialCash: (map['initialCash'] as num).toDouble(),
      finalCash: (map['finalCash'] as num).toDouble(),
      initialOvo: (map['initialOvo'] as num).toDouble(),
      finalOvo: (map['finalOvo'] as num).toDouble(),
      initialBri: (map['initialBri'] as num).toDouble(),
      finalBri: (map['finalBri'] as num).toDouble(),
      initialRekpon: (map['initialRekpon'] as num).toDouble(),
      finalRekpon: (map['finalRekpon'] as num).toDouble(),
      initialCapital: (map['initialCapital'] as num).toDouble(),
      finalResult: (map['finalResult'] as num).toDouble(),
    );
  }

  /// Convert to map
  Map<String, dynamic> toMap() {
    return {
      'uuid': uuid,
      'date': date,
      'initialGopay': initialGopay,
      'finalGopay': finalGopay,
      'initialBca': initialBca,
      'finalBca': finalBca,
      'initialCash': initialCash,
      'finalCash': finalCash,
      'initialOvo': initialOvo,
      'finalOvo': finalOvo,
      'initialBri': initialBri,
      'finalBri': finalBri,
      'initialRekpon': initialRekpon,
      'finalRekpon': finalRekpon,
      'initialCapital': initialCapital,
      'finalResult': finalResult,
    };
  }

  @override
  String toString() {
    return 'UpdateIncomeParams('
        'uuid: $uuid, '
        'date: $date, '
        'initialCapital: $initialCapital, '
        'finalResult: $finalResult'
        ')';
  }
}
