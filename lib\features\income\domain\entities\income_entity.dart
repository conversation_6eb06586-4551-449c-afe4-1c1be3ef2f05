import 'package:freezed_annotation/freezed_annotation.dart';

part 'income_entity.freezed.dart';
part 'income_entity.g.dart';

/// Income domain entity representing a single income record
///
/// This entity encapsulates all the business logic and rules for income data.
/// It's immutable and provides computed properties for derived values.
@freezed
class IncomeEntity with _$IncomeEntity {
  const factory IncomeEntity({
    required String uuid,
    required int id,
    required DateTime date,
    required int initialMileage,
    required int finalMileage,
    required double initialGopay,
    required double initialBca,
    required double initialCash,
    required double initialOvo,
    required double initialBri,
    required double initialRekpon,
    required double finalGopay,
    required double finalBca,
    required double finalCash,
    required double finalOvo,
    required double finalBri,
    required double finalRekpon,
    required double initialCapital,
    required double finalResult,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    @Default('pendingUpload') String syncStatus,
  }) = _IncomeEntity;

  const IncomeEntity._();

  /// Create from JSON
  factory IncomeEntity.fromJson(Map<String, dynamic> json) =>
      _$IncomeEntityFromJson(json);

  /// Calculate mileage difference
  int get mileage => finalMileage - initialMileage;

  /// Calculate total initial amount across all payment methods
  double get totalInitialAmount =>
      initialGopay +
      initialBca +
      initialCash +
      initialOvo +
      initialBri +
      initialRekpon;

  /// Calculate total final amount across all payment methods
  double get totalFinalAmount =>
      finalGopay + finalBca + finalCash + finalOvo + finalBri + finalRekpon;

  /// Calculate net income (total final - total initial)
  double get netIncome => totalFinalAmount - totalInitialAmount;

  /// Calculate profit/loss
  double get profitLoss => netIncome;

  /// Check if record is deleted
  bool get isDeleted => deletedAt != null;

  /// Check if record needs sync
  bool get needsSync => syncStatus == 'pendingUpload' || syncStatus == 'failed';

  /// Check if record is synced
  bool get isSynced => syncStatus == 'synced';

  /// Get formatted date string
  String get formattedDate =>
      '${date.day.toString().padLeft(2, '0')}/'
      '${date.month.toString().padLeft(2, '0')}/'
      '${date.year}';

  /// Get formatted date with time
  String get formattedDateTime =>
      '${formattedDate} '
      '${date.hour.toString().padLeft(2, '0')}:'
      '${date.minute.toString().padLeft(2, '0')}';

  /// Get payment method breakdown with differences
  Map<String, PaymentMethodData> get paymentBreakdown => {
    'GoPay': PaymentMethodData(
      name: 'GoPay',
      initial: initialGopay,
      finalAmount: finalGopay,
      difference: finalGopay - initialGopay,
    ),
    'BCA': PaymentMethodData(
      name: 'BCA',
      initial: initialBca,
      finalAmount: finalBca,
      difference: finalBca - initialBca,
    ),
    'Cash': PaymentMethodData(
      name: 'Cash',
      initial: initialCash,
      finalAmount: finalCash,
      difference: finalCash - initialCash,
    ),
    'OVO': PaymentMethodData(
      name: 'OVO',
      initial: initialOvo,
      finalAmount: finalOvo,
      difference: finalOvo - initialOvo,
    ),
    'BRI': PaymentMethodData(
      name: 'BRI',
      initial: initialBri,
      finalAmount: finalBri,
      difference: finalBri - initialBri,
    ),
    'Rekpon': PaymentMethodData(
      name: 'Rekpon',
      initial: initialRekpon,
      finalAmount: finalRekpon,
      difference: finalRekpon - initialRekpon,
    ),
  };

  /// Get positive payment methods (those with positive difference)
  List<PaymentMethodData> get positivePaymentMethods =>
      paymentBreakdown.values.where((method) => method.difference > 0).toList();

  /// Get negative payment methods (those with negative difference)
  List<PaymentMethodData> get negativePaymentMethods =>
      paymentBreakdown.values.where((method) => method.difference < 0).toList();

  /// Validate the income entity
  List<String> validate() {
    final errors = <String>[];

    // Validate mileage
    if (initialMileage < 0) {
      errors.add('Initial mileage cannot be negative');
    }
    if (finalMileage < initialMileage) {
      errors.add('Final mileage cannot be less than initial mileage');
    }

    // Validate monetary values
    final monetaryFields = [
      ('Initial GoPay', initialGopay),
      ('Initial BCA', initialBca),
      ('Initial Cash', initialCash),
      ('Initial OVO', initialOvo),
      ('Initial BRI', initialBri),
      ('Initial Rekpon', initialRekpon),
      ('Final GoPay', finalGopay),
      ('Final BCA', finalBca),
      ('Final Cash', finalCash),
      ('Final OVO', finalOvo),
      ('Final BRI', finalBri),
      ('Final Rekpon', finalRekpon),
      ('Initial Capital', initialCapital),
      ('Final Result', finalResult),
    ];

    for (final (name, value) in monetaryFields) {
      if (value < 0) {
        errors.add('$name cannot be negative');
      }
    }

    // Validate sync status
    const validSyncStatuses = ['pendingUpload', 'synced', 'failed', 'conflict'];
    if (!validSyncStatuses.contains(syncStatus)) {
      errors.add('Invalid sync status: $syncStatus');
    }

    // Validate dates
    if (createdAt.isAfter(DateTime.now())) {
      errors.add('Created date cannot be in the future');
    }
    if (updatedAt.isBefore(createdAt)) {
      errors.add('Updated date cannot be before created date');
    }

    return errors;
  }

  /// Check if the entity is valid
  bool get isValid => validate().isEmpty;
}

/// Payment method data helper class
@freezed
class PaymentMethodData with _$PaymentMethodData {
  const factory PaymentMethodData({
    required String name,
    required double initial,
    required double finalAmount,
    required double difference,
  }) = _PaymentMethodData;

  const PaymentMethodData._();

  /// Create from JSON
  factory PaymentMethodData.fromJson(Map<String, dynamic> json) =>
      _$PaymentMethodDataFromJson(json);

  /// Get formatted difference with sign
  String get formattedDifference {
    final sign = difference >= 0 ? '+' : '';
    return '$sign${difference.toStringAsFixed(2)}';
  }

  /// Check if this payment method had a positive change
  bool get isPositive => difference > 0;

  /// Check if this payment method had a negative change
  bool get isNegative => difference < 0;

  /// Check if this payment method had no change
  bool get isNeutral => difference == 0;
}
