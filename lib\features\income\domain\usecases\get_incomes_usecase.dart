import '../entities/income_entity.dart';
import '../repositories/income_repository.dart';

/// Use case for retrieving income records
/// 
/// This use case handles the business logic for fetching income records
/// with various filtering and sorting options.
class GetIncomesUseCase {
  final IncomeRepository _repository;

  GetIncomesUseCase(this._repository);

  /// Get all income records
  Future<List<IncomeEntity>> execute() async {
    return await _repository.getAllIncomes();
  }

  /// Get income records by date range
  Future<List<IncomeEntity>> executeByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    // Validate date range
    if (startDate.isAfter(endDate)) {
      throw ArgumentError('Start date cannot be after end date');
    }

    if (endDate.isAfter(DateTime.now())) {
      throw ArgumentError('End date cannot be in the future');
    }

    return await _repository.getIncomesByDateRange(startDate, endDate);
  }

  /// Get income records with pagination
  Future<PaginatedIncomes> executeWithPagination(
    GetIncomesParams params,
  ) async {
    // Validate parameters
    _validateParams(params);

    return await _repository.getIncomesPaginated(
      page: params.page,
      limit: params.limit,
      startDate: params.startDate,
      endDate: params.endDate,
      sortBy: params.sortBy,
      ascending: params.ascending,
    );
  }

  /// Search income records
  Future<List<IncomeEntity>> executeSearch(
    IncomeSearchCriteria criteria,
  ) async {
    return await _repository.searchIncomes(criteria);
  }

  /// Get income statistics
  Future<IncomeStats> executeStats(
    DateTime startDate,
    DateTime endDate,
  ) async {
    // Validate date range
    if (startDate.isAfter(endDate)) {
      throw ArgumentError('Start date cannot be after end date');
    }

    if (endDate.isAfter(DateTime.now())) {
      throw ArgumentError('End date cannot be in the future');
    }

    return await _repository.getIncomeStats(startDate, endDate);
  }

  /// Validate parameters
  void _validateParams(GetIncomesParams params) {
    if (params.page < 1) {
      throw ArgumentError('Page number must be greater than 0');
    }

    if (params.limit < 1 || params.limit > 100) {
      throw ArgumentError('Limit must be between 1 and 100');
    }

    if (params.startDate != null && params.endDate != null) {
      if (params.startDate!.isAfter(params.endDate!)) {
        throw ArgumentError('Start date cannot be after end date');
      }

      if (params.endDate!.isAfter(DateTime.now())) {
        throw ArgumentError('End date cannot be in the future');
      }
    }
  }
}

/// Parameters for getting income records
class GetIncomesParams {
  final int page;
  final int limit;
  final DateTime? startDate;
  final DateTime? endDate;
  final String? sortBy;
  final bool ascending;

  const GetIncomesParams({
    this.page = 1,
    this.limit = 20,
    this.startDate,
    this.endDate,
    this.sortBy,
    this.ascending = false,
  });

  /// Create with date range
  factory GetIncomesParams.withDateRange(
    DateTime startDate,
    DateTime endDate, {
    int page = 1,
    int limit = 20,
    String? sortBy,
    bool ascending = false,
  }) {
    return GetIncomesParams(
      page: page,
      limit: limit,
      startDate: startDate,
      endDate: endDate,
      sortBy: sortBy,
      ascending: ascending,
    );
  }

  /// Create for current month
  factory GetIncomesParams.currentMonth({
    int page = 1,
    int limit = 20,
    String? sortBy,
    bool ascending = false,
  }) {
    final now = DateTime.now();
    final startDate = DateTime(now.year, now.month, 1);
    final endDate = DateTime(now.year, now.month + 1, 0);

    return GetIncomesParams(
      page: page,
      limit: limit,
      startDate: startDate,
      endDate: endDate,
      sortBy: sortBy,
      ascending: ascending,
    );
  }

  /// Create for current week
  factory GetIncomesParams.currentWeek({
    int page = 1,
    int limit = 20,
    String? sortBy,
    bool ascending = false,
  }) {
    final now = DateTime.now();
    final startDate = now.subtract(Duration(days: now.weekday - 1));
    final endDate = startDate.add(const Duration(days: 6));

    return GetIncomesParams(
      page: page,
      limit: limit,
      startDate: DateTime(startDate.year, startDate.month, startDate.day),
      endDate: DateTime(endDate.year, endDate.month, endDate.day, 23, 59, 59),
      sortBy: sortBy,
      ascending: ascending,
    );
  }

  /// Create for today
  factory GetIncomesParams.today({
    int page = 1,
    int limit = 20,
    String? sortBy,
    bool ascending = false,
  }) {
    final now = DateTime.now();
    final startDate = DateTime(now.year, now.month, now.day);
    final endDate = DateTime(now.year, now.month, now.day, 23, 59, 59);

    return GetIncomesParams(
      page: page,
      limit: limit,
      startDate: startDate,
      endDate: endDate,
      sortBy: sortBy,
      ascending: ascending,
    );
  }

  /// Copy with new parameters
  GetIncomesParams copyWith({
    int? page,
    int? limit,
    DateTime? startDate,
    DateTime? endDate,
    String? sortBy,
    bool? ascending,
  }) {
    return GetIncomesParams(
      page: page ?? this.page,
      limit: limit ?? this.limit,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      sortBy: sortBy ?? this.sortBy,
      ascending: ascending ?? this.ascending,
    );
  }

  @override
  String toString() {
    return 'GetIncomesParams('
        'page: $page, '
        'limit: $limit, '
        'startDate: $startDate, '
        'endDate: $endDate, '
        'sortBy: $sortBy, '
        'ascending: $ascending'
        ')';
  }
}
