import 'package:uuid/uuid.dart';
import '../../domain/entities/income_entity.dart';
import '../../domain/repositories/income_repository.dart';
import '../models/income_model.dart';
import '../../../../core/datasources/database.dart';

/// Concrete implementation of IncomeRepository using local database
class IncomeRepositoryImpl implements IncomeRepository {
  final AppDatabase _database;
  final Uuid _uuid = const Uuid();

  IncomeRepositoryImpl(this._database);

  @override
  Future<List<IncomeEntity>> getAllIncomes() async {
    final records = await _database.getAllIncomeRecords();
    return records
        .where((record) => record.deletedAt == null)
        .map((record) => IncomeModel.fromDatabase(record).toEntity())
        .toList();
  }

  @override
  Future<List<IncomeEntity>> getIncomesByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final records = await _database.getIncomeByDateRange(startDate, endDate);
    return records
        .where((record) => record.deletedAt == null)
        .map((record) => IncomeModel.fromDatabase(record).toEntity())
        .toList();
  }

  @override
  Future<IncomeEntity?> getIncomeByUuid(String uuid) async {
    final record = await _database.getIncomeByUuid(uuid);
    if (record == null || record.deletedAt != null) {
      return null;
    }
    return IncomeModel.fromDatabase(record).toEntity();
  }

  @override
  Future<IncomeEntity> createIncome(IncomeEntity income) async {
    // Generate UUID if not provided
    final uuid = income.uuid.isEmpty ? _uuid.v4() : income.uuid;
    final now = DateTime.now();
    
    final incomeWithMetadata = income.copyWith(
      uuid: uuid,
      createdAt: now,
      updatedAt: now,
      syncStatus: 'pendingUpload',
    );

    final model = IncomeModel.fromEntity(incomeWithMetadata);
    final companion = model.toCompanion();
    
    await _database.insertIncomeRecord(companion);
    
    // Return the created entity with the generated ID
    final createdRecord = await _database.getIncomeByUuid(uuid);
    if (createdRecord == null) {
      throw Exception('Failed to create income record');
    }
    
    return IncomeModel.fromDatabase(createdRecord).toEntity();
  }

  @override
  Future<IncomeEntity> updateIncome(IncomeEntity income) async {
    final now = DateTime.now();
    final updatedIncome = income.copyWith(
      updatedAt: now,
      syncStatus: 'pendingUpload',
    );

    final model = IncomeModel.fromEntity(updatedIncome);
    final companion = model.toUpdateCompanion();
    
    final success = await _database.updateIncomeRecord(companion);
    if (!success) {
      throw Exception('Failed to update income record');
    }
    
    // Return the updated entity
    final updatedRecord = await _database.getIncomeByUuid(income.uuid);
    if (updatedRecord == null) {
      throw Exception('Income record not found after update');
    }
    
    return IncomeModel.fromDatabase(updatedRecord).toEntity();
  }

  @override
  Future<void> deleteIncome(String uuid) async {
    final deletedCount = await _database.deleteIncomeRecord(uuid);
    if (deletedCount == 0) {
      throw Exception('Income record not found or already deleted');
    }
  }

  @override
  Future<IncomeEntity> softDeleteIncome(String uuid) async {
    final deletedCount = await _database.softDeleteIncomeRecord(uuid);
    if (deletedCount == 0) {
      throw Exception('Income record not found');
    }
    
    // Return the soft-deleted entity
    final deletedRecord = await _database.getIncomeByUuid(uuid);
    if (deletedRecord == null) {
      throw Exception('Income record not found after soft delete');
    }
    
    return IncomeModel.fromDatabase(deletedRecord).toEntity();
  }

  @override
  Future<List<IncomeEntity>> getPendingSyncIncomes() async {
    final records = await _database.getPendingSyncRecords();
    return records
        .map((record) => IncomeModel.fromDatabase(record).toEntity())
        .toList();
  }

  @override
  Future<IncomeEntity> updateSyncStatus(String uuid, String status) async {
    final updatedCount = await _database.updateSyncStatus(uuid, status);
    if (updatedCount == 0) {
      throw Exception('Income record not found');
    }
    
    // Return the updated entity
    final updatedRecord = await _database.getIncomeByUuid(uuid);
    if (updatedRecord == null) {
      throw Exception('Income record not found after sync status update');
    }
    
    return IncomeModel.fromDatabase(updatedRecord).toEntity();
  }

  @override
  Future<IncomeStats> getIncomeStats(DateTime startDate, DateTime endDate) async {
    final stats = await _database.getIncomeStats(startDate, endDate);
    final incomes = await getIncomesByDateRange(startDate, endDate);
    
    // Calculate payment method breakdown
    final paymentBreakdown = <String, double>{};
    for (final income in incomes) {
      final breakdown = income.paymentBreakdown;
      for (final entry in breakdown.entries) {
        paymentBreakdown[entry.key] = 
            (paymentBreakdown[entry.key] ?? 0.0) + entry.value.difference;
      }
    }
    
    final daysDifference = endDate.difference(startDate).inDays + 1;
    final totalMileage = stats['totalMileage'] ?? 0.0;
    
    return IncomeStats(
      totalIncome: stats['totalIncome'] ?? 0.0,
      totalMileage: totalMileage,
      totalRecords: (stats['totalRecords'] ?? 0.0).toInt(),
      averageIncomePerDay: daysDifference > 0 
          ? (stats['totalIncome'] ?? 0.0) / daysDifference 
          : 0.0,
      averageIncomePerMile: totalMileage > 0 
          ? (stats['totalIncome'] ?? 0.0) / totalMileage 
          : 0.0,
      paymentMethodBreakdown: paymentBreakdown,
    );
  }

  @override
  Future<List<IncomeEntity>> searchIncomes(IncomeSearchCriteria criteria) async {
    // Start with all incomes
    var incomes = await getAllIncomes();
    
    // Apply date range filter
    if (criteria.startDate != null || criteria.endDate != null) {
      incomes = incomes.where((income) {
        if (criteria.startDate != null && income.date.isBefore(criteria.startDate!)) {
          return false;
        }
        if (criteria.endDate != null && income.date.isAfter(criteria.endDate!)) {
          return false;
        }
        return true;
      }).toList();
    }
    
    // Apply income range filter
    if (criteria.minIncome != null || criteria.maxIncome != null) {
      incomes = incomes.where((income) {
        if (criteria.minIncome != null && income.netIncome < criteria.minIncome!) {
          return false;
        }
        if (criteria.maxIncome != null && income.netIncome > criteria.maxIncome!) {
          return false;
        }
        return true;
      }).toList();
    }
    
    // Apply mileage range filter
    if (criteria.minMileage != null || criteria.maxMileage != null) {
      incomes = incomes.where((income) {
        if (criteria.minMileage != null && income.mileage < criteria.minMileage!) {
          return false;
        }
        if (criteria.maxMileage != null && income.mileage > criteria.maxMileage!) {
          return false;
        }
        return true;
      }).toList();
    }
    
    // Apply sync status filter
    if (criteria.syncStatuses != null && criteria.syncStatuses!.isNotEmpty) {
      incomes = incomes.where((income) {
        return criteria.syncStatuses!.contains(income.syncStatus);
      }).toList();
    }
    
    return incomes;
  }

  @override
  Future<PaginatedIncomes> getIncomesPaginated({
    int page = 1,
    int limit = 20,
    DateTime? startDate,
    DateTime? endDate,
    String? sortBy,
    bool ascending = false,
  }) async {
    // Get filtered incomes
    List<IncomeEntity> incomes;
    if (startDate != null && endDate != null) {
      incomes = await getIncomesByDateRange(startDate, endDate);
    } else {
      incomes = await getAllIncomes();
    }
    
    // Apply sorting
    if (sortBy != null) {
      incomes.sort((a, b) {
        int comparison = 0;
        switch (sortBy) {
          case 'date':
            comparison = a.date.compareTo(b.date);
            break;
          case 'netIncome':
            comparison = a.netIncome.compareTo(b.netIncome);
            break;
          case 'mileage':
            comparison = a.mileage.compareTo(b.mileage);
            break;
          default:
            comparison = a.date.compareTo(b.date);
        }
        return ascending ? comparison : -comparison;
      });
    }
    
    // Calculate pagination
    final totalRecords = incomes.length;
    final totalPages = (totalRecords / limit).ceil();
    final startIndex = (page - 1) * limit;
    final endIndex = (startIndex + limit).clamp(0, totalRecords);
    
    final paginatedIncomes = incomes.sublist(
      startIndex.clamp(0, totalRecords),
      endIndex,
    );
    
    return PaginatedIncomes(
      incomes: paginatedIncomes,
      currentPage: page,
      totalPages: totalPages,
      totalRecords: totalRecords,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
    );
  }
}
