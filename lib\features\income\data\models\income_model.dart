import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:uuid/uuid.dart';
import 'package:drift/drift.dart';
import '../../domain/entities/income_entity.dart';
import '../../../../core/datasources/database.dart';

part 'income_model.freezed.dart';
part 'income_model.g.dart';

/// Income data model for API and database operations
///
/// This model handles the conversion between different data representations:
/// - Database records (Drift)
/// - Domain entities (business logic)
/// - JSON (API communication)
@freezed
class IncomeModel with _$IncomeModel {
  const factory IncomeModel({
    required String uuid,
    required int id,
    required DateTime date,
    required int initialMileage,
    required int finalMileage,
    required double initialGopay,
    required double initialBca,
    required double initialCash,
    required double initialOvo,
    required double initialBri,
    required double initialRekpon,
    required double finalGopay,
    required double finalBca,
    required double finalCash,
    required double finalOvo,
    required double finalBri,
    required double finalRekpon,
    required double initialCapital,
    required double finalResult,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
    @Default('pendingUpload') String syncStatus,
  }) = _IncomeModel;

  const IncomeModel._();

  /// Create from JSON
  factory IncomeModel.fromJson(Map<String, dynamic> json) =>
      _$IncomeModelFromJson(json);

  /// Create from database record
  factory IncomeModel.fromDatabase(IncomeTableData data) {
    return IncomeModel(
      uuid: data.uuid,
      id: data.id,
      date: data.date,
      initialMileage: data.initialMileage,
      finalMileage: data.finalMileage,
      initialGopay: data.initialGopay,
      initialBca: data.initialBca,
      initialCash: data.initialCash,
      initialOvo: data.initialOvo,
      initialBri: data.initialBri,
      initialRekpon: data.initialRekpon,
      finalGopay: data.finalGopay,
      finalBca: data.finalBca,
      finalCash: data.finalCash,
      finalOvo: data.finalOvo,
      finalBri: data.finalBri,
      finalRekpon: data.finalRekpon,
      initialCapital: data.initialCapital,
      finalResult: data.finalResult,
      createdAt: data.createdAt,
      updatedAt: data.updatedAt,
      deletedAt: data.deletedAt,
      syncStatus: data.syncStatus,
    );
  }

  /// Create from domain entity
  factory IncomeModel.fromEntity(IncomeEntity entity) {
    return IncomeModel(
      uuid: entity.uuid,
      id: entity.id,
      date: entity.date,
      initialMileage: entity.initialMileage,
      finalMileage: entity.finalMileage,
      initialGopay: entity.initialGopay,
      initialBca: entity.initialBca,
      initialCash: entity.initialCash,
      initialOvo: entity.initialOvo,
      initialBri: entity.initialBri,
      initialRekpon: entity.initialRekpon,
      finalGopay: entity.finalGopay,
      finalBca: entity.finalBca,
      finalCash: entity.finalCash,
      finalOvo: entity.finalOvo,
      finalBri: entity.finalBri,
      finalRekpon: entity.finalRekpon,
      initialCapital: entity.initialCapital,
      finalResult: entity.finalResult,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
      deletedAt: entity.deletedAt,
      syncStatus: entity.syncStatus,
    );
  }

  /// Convert to domain entity
  IncomeEntity toEntity() {
    return IncomeEntity(
      uuid: uuid,
      id: id,
      date: date,
      initialMileage: initialMileage,
      finalMileage: finalMileage,
      initialGopay: initialGopay,
      initialBca: initialBca,
      initialCash: initialCash,
      initialOvo: initialOvo,
      initialBri: initialBri,
      initialRekpon: initialRekpon,
      finalGopay: finalGopay,
      finalBca: finalBca,
      finalCash: finalCash,
      finalOvo: finalOvo,
      finalBri: finalBri,
      finalRekpon: finalRekpon,
      initialCapital: initialCapital,
      finalResult: finalResult,
      createdAt: createdAt,
      updatedAt: updatedAt,
      deletedAt: deletedAt,
      syncStatus: syncStatus,
    );
  }

  /// Convert to database companion for inserts/updates
  IncomeTableCompanion toCompanion() {
    return IncomeTableCompanion(
      uuid: Value(uuid),
      id: Value(id),
      date: Value(date),
      initialMileage: Value(initialMileage),
      finalMileage: Value(finalMileage),
      initialGopay: Value(initialGopay),
      initialBca: Value(initialBca),
      initialCash: Value(initialCash),
      initialOvo: Value(initialOvo),
      initialBri: Value(initialBri),
      initialRekpon: Value(initialRekpon),
      finalGopay: Value(finalGopay),
      finalBca: Value(finalBca),
      finalCash: Value(finalCash),
      finalOvo: Value(finalOvo),
      finalBri: Value(finalBri),
      finalRekpon: Value(finalRekpon),
      initialCapital: Value(initialCapital),
      finalResult: Value(finalResult),
      createdAt: Value(createdAt),
      updatedAt: Value(updatedAt),
      deletedAt: Value.absentIfNull(deletedAt),
      syncStatus: Value(syncStatus),
    );
  }

  /// Convert to database companion for updates (excluding auto-generated fields)
  IncomeTableCompanion toUpdateCompanion() {
    return IncomeTableCompanion(
      uuid: Value(uuid),
      date: Value(date),
      initialMileage: Value(initialMileage),
      finalMileage: Value(finalMileage),
      initialGopay: Value(initialGopay),
      initialBca: Value(initialBca),
      initialCash: Value(initialCash),
      initialOvo: Value(initialOvo),
      initialBri: Value(initialBri),
      initialRekpon: Value(initialRekpon),
      finalGopay: Value(finalGopay),
      finalBca: Value(finalBca),
      finalCash: Value(finalCash),
      finalOvo: Value(finalOvo),
      finalBri: Value(finalBri),
      finalRekpon: Value(finalRekpon),
      initialCapital: Value(initialCapital),
      finalResult: Value(finalResult),
      updatedAt: Value(DateTime.now()),
      deletedAt: Value.absentIfNull(deletedAt),
      syncStatus: Value(syncStatus),
    );
  }

  /// Create a copy with updated sync status
  IncomeModel copyWithSyncStatus(String status) {
    return copyWith(syncStatus: status, updatedAt: DateTime.now());
  }

  /// Create a copy marked as deleted
  IncomeModel copyWithDeleted() {
    final now = DateTime.now();
    return copyWith(
      deletedAt: now,
      updatedAt: now,
      syncStatus: 'pendingUpload',
    );
  }
}

/// Income creation request model
@freezed
class CreateIncomeRequest with _$CreateIncomeRequest {
  const factory CreateIncomeRequest({
    required DateTime date,
    required int initialMileage,
    required int finalMileage,
    required double initialGopay,
    required double initialBca,
    required double initialCash,
    required double initialOvo,
    required double initialBri,
    required double initialRekpon,
    required double finalGopay,
    required double finalBca,
    required double finalCash,
    required double finalOvo,
    required double finalBri,
    required double finalRekpon,
    required double initialCapital,
    required double finalResult,
  }) = _CreateIncomeRequest;

  const CreateIncomeRequest._();

  /// Create from JSON
  factory CreateIncomeRequest.fromJson(Map<String, dynamic> json) =>
      _$CreateIncomeRequestFromJson(json);

  /// Convert to IncomeModel with generated UUID and timestamps
  IncomeModel toModel() {
    final now = DateTime.now();
    return IncomeModel(
      uuid: _generateUuid(),
      id: 0, // Will be auto-generated by database
      date: date,
      initialMileage: initialMileage,
      finalMileage: finalMileage,
      initialGopay: initialGopay,
      initialBca: initialBca,
      initialCash: initialCash,
      initialOvo: initialOvo,
      initialBri: initialBri,
      initialRekpon: initialRekpon,
      finalGopay: finalGopay,
      finalBca: finalBca,
      finalCash: finalCash,
      finalOvo: finalOvo,
      finalBri: finalBri,
      finalRekpon: finalRekpon,
      initialCapital: initialCapital,
      finalResult: finalResult,
      createdAt: now,
      updatedAt: now,
      syncStatus: 'pendingUpload',
    );
  }
}

/// Simple UUID generator using the uuid package
String _generateUuid() {
  return const Uuid().v4();
}
