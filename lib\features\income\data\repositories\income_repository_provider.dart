import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../domain/repositories/income_repository.dart';
import 'income_repository_impl.dart';
import '../../../../core/providers/database_provider.dart';

part 'income_repository_provider.g.dart';

/// Income repository provider
/// 
/// This provider creates and manages the income repository instance.
/// It depends on the database provider to get the database connection.
@Riverpod(keepAlive: true)
IncomeRepository incomeRepository(IncomeRepositoryRef ref) {
  final database = ref.watch(databaseProvider);
  return IncomeRepositoryImpl(database);
}
