import '../entities/income_entity.dart';

/// Abstract repository interface for income operations
/// 
/// This interface defines the contract for income data operations,
/// allowing for different implementations (local database, remote API, etc.)
abstract class IncomeRepository {
  /// Get all income records
  Future<List<IncomeEntity>> getAllIncomes();

  /// Get income records by date range
  Future<List<IncomeEntity>> getIncomesByDateRange(
    DateTime startDate,
    DateTime endDate,
  );

  /// Get income record by UUID
  Future<IncomeEntity?> getIncomeByUuid(String uuid);

  /// Create new income record
  Future<IncomeEntity> createIncome(IncomeEntity income);

  /// Update existing income record
  Future<IncomeEntity> updateIncome(IncomeEntity income);

  /// Delete income record by UUID
  Future<void> deleteIncome(String uuid);

  /// Soft delete income record (mark as deleted)
  Future<IncomeEntity> softDeleteIncome(String uuid);

  /// Get income records pending sync
  Future<List<IncomeEntity>> getPendingSyncIncomes();

  /// Update sync status of income record
  Future<IncomeEntity> updateSyncStatus(String uuid, String status);

  /// Get income statistics for a date range
  Future<IncomeStats> getIncomeStats(DateTime startDate, DateTime endDate);

  /// Search income records by criteria
  Future<List<IncomeEntity>> searchIncomes(IncomeSearchCriteria criteria);

  /// Get income records with pagination
  Future<PaginatedIncomes> getIncomesPaginated({
    int page = 1,
    int limit = 20,
    DateTime? startDate,
    DateTime? endDate,
    String? sortBy,
    bool ascending = false,
  });
}

/// Income statistics data class
class IncomeStats {
  final double totalIncome;
  final double totalMileage;
  final int totalRecords;
  final double averageIncomePerDay;
  final double averageIncomePerMile;
  final Map<String, double> paymentMethodBreakdown;

  const IncomeStats({
    required this.totalIncome,
    required this.totalMileage,
    required this.totalRecords,
    required this.averageIncomePerDay,
    required this.averageIncomePerMile,
    required this.paymentMethodBreakdown,
  });
}

/// Income search criteria
class IncomeSearchCriteria {
  final DateTime? startDate;
  final DateTime? endDate;
  final double? minIncome;
  final double? maxIncome;
  final int? minMileage;
  final int? maxMileage;
  final List<String>? syncStatuses;
  final String? searchText;

  const IncomeSearchCriteria({
    this.startDate,
    this.endDate,
    this.minIncome,
    this.maxIncome,
    this.minMileage,
    this.maxMileage,
    this.syncStatuses,
    this.searchText,
  });
}

/// Paginated income results
class PaginatedIncomes {
  final List<IncomeEntity> incomes;
  final int currentPage;
  final int totalPages;
  final int totalRecords;
  final bool hasNextPage;
  final bool hasPreviousPage;

  const PaginatedIncomes({
    required this.incomes,
    required this.currentPage,
    required this.totalPages,
    required this.totalRecords,
    required this.hasNextPage,
    required this.hasPreviousPage,
  });
}
