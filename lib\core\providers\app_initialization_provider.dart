import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/app_initialization_service.dart';

part 'app_initialization_provider.g.dart';

/// App initialization service provider
/// 
/// This provider creates and manages the app initialization service instance.
@Riverpod(keepAlive: true)
AppInitializationService appInitializationService(Ref ref) {
  return AppInitializationService.instance;
}

/// App initialization provider
/// 
/// This provider handles the app initialization process and returns the status.
@riverpod
Future<bool> appInitialization(Ref ref) async {
  final service = ref.watch(appInitializationServiceProvider);
  
  try {
    await service.initialize();
    return true;
  } on Exception catch (e) {
    // Log the error for debugging
    print('App initialization failed: $e');
    return false;
  }
}

/// App initialization status provider
/// 
/// This provider provides the current initialization status.
@riverpod
InitializationStatus appInitializationStatus(Ref ref) {
  final service = ref.watch(appInitializationServiceProvider);
  return service.getStatus();
}
