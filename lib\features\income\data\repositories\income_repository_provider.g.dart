// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'income_repository_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$incomeRepositoryHash() => r'34bc5d484816d8bcc9aab6749ecf34cbfef25789';

/// Income repository provider
///
/// This provider creates and manages the income repository instance.
/// It depends on the database provider to get the database connection.
///
/// Copied from [incomeRepository].
@ProviderFor(incomeRepository)
final incomeRepositoryProvider = Provider<IncomeRepository>.internal(
  incomeRepository,
  name: r'incomeRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$incomeRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef IncomeRepositoryRef = ProviderRef<IncomeRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
