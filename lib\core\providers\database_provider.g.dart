// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'database_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$databaseHash() => r'f05ed6182d1c16b9fbfa21907ec0352a54f6ce1b';

/// Global database provider
///
/// This provider creates and manages the application database instance.
/// It's a singleton that will be created once and reused throughout the app.
///
/// Copied from [database].
@ProviderFor(database)
final databaseProvider = Provider<AppDatabase>.internal(
  database,
  name: r'databaseProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$databaseHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef DatabaseRef = ProviderRef<AppDatabase>;
String _$databaseServiceHash() => r'e0eeb57a46c4332cd9683224b259a22afb2cd152';

/// Database service provider
///
/// This provider creates and manages the database service instance.
///
/// Copied from [databaseService].
@ProviderFor(databaseService)
final databaseServiceProvider = Provider<DatabaseService>.internal(
  databaseService,
  name: r'databaseServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$databaseServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef DatabaseServiceRef = ProviderRef<DatabaseService>;
String _$databaseInitializationHash() =>
    r'0be50f7cca0751ec1caeaf47ffcd169867cdfe5e';

/// Database initialization provider
///
/// This provider handles database initialization and returns the initialized state.
///
/// Copied from [databaseInitialization].
@ProviderFor(databaseInitialization)
final databaseInitializationProvider = AutoDisposeFutureProvider<bool>.internal(
  databaseInitialization,
  name: r'databaseInitializationProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$databaseInitializationHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef DatabaseInitializationRef = AutoDisposeFutureProviderRef<bool>;
String _$databaseConnectionStateHash() =>
    r'6c7a23e4416fb94bc944d1bcae4b029180a6e971';

/// Database connection state provider
///
/// This provider tracks the database connection state for UI feedback
///
/// Copied from [databaseConnectionState].
@ProviderFor(databaseConnectionState)
final databaseConnectionStateProvider =
    AutoDisposeFutureProvider<bool>.internal(
      databaseConnectionState,
      name: r'databaseConnectionStateProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$databaseConnectionStateHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef DatabaseConnectionStateRef = AutoDisposeFutureProviderRef<bool>;
String _$databaseStatsHash() => r'f086e9b738e1ad5af5e3ff1fbd3668f8da026d1c';

/// Database statistics provider
///
/// This provider provides database statistics for monitoring
///
/// Copied from [databaseStats].
@ProviderFor(databaseStats)
final databaseStatsProvider = AutoDisposeFutureProvider<DatabaseStats>.internal(
  databaseStats,
  name: r'databaseStatsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$databaseStatsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef DatabaseStatsRef = AutoDisposeFutureProviderRef<DatabaseStats>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
