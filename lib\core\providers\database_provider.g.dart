// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'database_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$databaseHash() => r'f05ed6182d1c16b9fbfa21907ec0352a54f6ce1b';

/// Global database provider
///
/// This provider creates and manages the application database instance.
/// It's a singleton that will be created once and reused throughout the app.
///
/// Copied from [database].
@ProviderFor(database)
final databaseProvider = Provider<AppDatabase>.internal(
  database,
  name: r'databaseProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$databaseHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef DatabaseRef = ProviderRef<AppDatabase>;
String _$databaseConnectionStateHash() =>
    r'9e958d935d7885232c7277b7e6ff0b8ebca71a7d';

/// Database connection state provider
///
/// This provider tracks the database connection state for UI feedback
///
/// Copied from [databaseConnectionState].
@ProviderFor(databaseConnectionState)
final databaseConnectionStateProvider =
    AutoDisposeFutureProvider<bool>.internal(
      databaseConnectionState,
      name: r'databaseConnectionStateProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$databaseConnectionStateHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef DatabaseConnectionStateRef = AutoDisposeFutureProviderRef<bool>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
