import 'dart:io';

import 'package:drift/drift.dart';
import 'package:drift/native.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as p;
import 'package:sqlite3/sqlite3.dart';
import 'package:sqlite3_flutter_libs/sqlite3_flutter_libs.dart';

// Import table definitions
import 'tables/income_table.dart';

// This will be generated by Drift
part 'database.g.dart';

/// Main database class for the Drivly application
@DriftDatabase(tables: [IncomeTable])
class AppDatabase extends _$AppDatabase {
  AppDatabase() : super(_openConnection());

  @override
  int get schemaVersion => 1;

  @override
  MigrationStrategy get migration {
    return MigrationStrategy(
      onCreate: (Migrator m) async {
        await m.createAll();
      },
      onUpgrade: (Migrator m, int from, int to) async {
        // Handle database migrations here
        if (from < 2) {
          // Example migration for future versions
          // await m.addColumn(incomeTable, incomeTable.newColumn);
        }
      },
      beforeOpen: (details) async {
        // Enable foreign keys
        await customStatement('PRAGMA foreign_keys = ON');
        
        // Enable WAL mode for better performance
        await customStatement('PRAGMA journal_mode = WAL');
        
        // Set synchronous mode to NORMAL for better performance
        await customStatement('PRAGMA synchronous = NORMAL');
        
        // Set cache size (negative value means KB)
        await customStatement('PRAGMA cache_size = -2000');
        
        // Set temp store to memory
        await customStatement('PRAGMA temp_store = MEMORY');
      },
    );
  }

  /// Get all income records
  Future<List<IncomeTableData>> getAllIncomeRecords() {
    return select(incomeTable).get();
  }

  /// Get income records by date range
  Future<List<IncomeTableData>> getIncomeByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) {
    return (select(incomeTable)
          ..where((tbl) => tbl.date.isBetweenValues(startDate, endDate))
          ..orderBy([(tbl) => OrderingTerm.desc(tbl.date)]))
        .get();
  }

  /// Get income record by UUID
  Future<IncomeTableData?> getIncomeByUuid(String uuid) {
    return (select(incomeTable)..where((tbl) => tbl.uuid.equals(uuid)))
        .getSingleOrNull();
  }

  /// Insert new income record
  Future<int> insertIncomeRecord(IncomeTableCompanion entry) {
    return into(incomeTable).insert(entry);
  }

  /// Update income record
  Future<bool> updateIncomeRecord(IncomeTableCompanion entry) {
    return update(incomeTable).replace(entry);
  }

  /// Delete income record by UUID
  Future<int> deleteIncomeRecord(String uuid) {
    return (delete(incomeTable)..where((tbl) => tbl.uuid.equals(uuid))).go();
  }

  /// Soft delete income record (set deleted_at timestamp)
  Future<int> softDeleteIncomeRecord(String uuid) {
    return (update(incomeTable)..where((tbl) => tbl.uuid.equals(uuid)))
        .write(IncomeTableCompanion(
      deletedAt: Value(DateTime.now()),
      updatedAt: Value(DateTime.now()),
    ));
  }

  /// Get income records pending sync
  Future<List<IncomeTableData>> getPendingSyncRecords() {
    return (select(incomeTable)
          ..where((tbl) => tbl.syncStatus.equals('pendingUpload')))
        .get();
  }

  /// Update sync status
  Future<int> updateSyncStatus(String uuid, String status) {
    return (update(incomeTable)..where((tbl) => tbl.uuid.equals(uuid)))
        .write(IncomeTableCompanion(
      syncStatus: Value(status),
      updatedAt: Value(DateTime.now()),
    ));
  }

  /// Get income statistics for a date range
  Future<Map<String, double>> getIncomeStats(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final query = selectOnly(incomeTable)
      ..addColumns([
        incomeTable.netIncome.sum(),
        incomeTable.mileage.sum(),
        incomeTable.uuid.count(),
      ])
      ..where(incomeTable.date.isBetweenValues(startDate, endDate))
      ..where(incomeTable.deletedAt.isNull());

    final result = await query.getSingle();
    
    return {
      'totalIncome': result.read(incomeTable.netIncome.sum()) ?? 0.0,
      'totalMileage': (result.read(incomeTable.mileage.sum()) ?? 0).toDouble(),
      'totalRecords': (result.read(incomeTable.uuid.count()) ?? 0).toDouble(),
    };
  }
}

/// Database connection configuration
LazyDatabase _openConnection() {
  return LazyDatabase(() async {
    // Ensure sqlite3 is properly initialized on mobile platforms
    if (Platform.isAndroid) {
      await applyWorkaroundToOpenSqlite3OnOldAndroidVersions();
    }

    // Get the application documents directory
    final dbFolder = await getApplicationDocumentsDirectory();
    final file = File(p.join(dbFolder.path, 'drivly_database.db'));

    // Configure and return the database connection
    return NativeDatabase.createInBackground(
      file,
      logStatements: true, // Enable SQL logging in debug mode
    );
  });
}
