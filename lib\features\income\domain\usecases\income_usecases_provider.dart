import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/repositories/income_repository_provider.dart';
import 'create_income_usecase.dart';
import 'update_income_usecase.dart';
import 'get_incomes_usecase.dart';
import 'delete_income_usecase.dart';

part 'income_usecases_provider.g.dart';

/// Create income use case provider
@riverpod
CreateIncomeUseCase createIncomeUseCase(Ref ref) {
  final repository = ref.watch(incomeRepositoryProvider);
  return CreateIncomeUseCase(repository);
}

/// Update income use case provider
@riverpod
UpdateIncomeUseCase updateIncomeUseCase(Ref ref) {
  final repository = ref.watch(incomeRepositoryProvider);
  return UpdateIncomeUseCase(repository);
}

/// Get incomes use case provider
@riverpod
GetIncomesUseCase getIncomesUseCase(Ref ref) {
  final repository = ref.watch(incomeRepositoryProvider);
  return GetIncomesUseCase(repository);
}

/// Delete income use case provider
@riverpod
DeleteIncomeUseCase deleteIncomeUseCase(Ref ref) {
  final repository = ref.watch(incomeRepositoryProvider);
  return DeleteIncomeUseCase(repository);
}
